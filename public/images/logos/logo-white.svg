<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 2573.96 725.8">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient-2);
      }

      .cls-2 {
        letter-spacing: -.07em;
      }

      .cls-3, .cls-4, .cls-5 {
        fill: #fff;
      }

      .cls-4 {
        font-size: 336.2px;
      }

      .cls-4, .cls-5 {
        font-family: JuliusSansOne-Regular, 'Julius Sans One';
      }

      .cls-6 {
        fill: url(#linear-gradient-4);
      }

      .cls-7 {
        fill: url(#linear-gradient-3);
      }

      .cls-8 {
        fill: url(#linear-gradient-5);
      }

      .cls-9 {
        letter-spacing: -.07em;
      }

      .cls-10 {
        letter-spacing: -.03em;
      }

      .cls-11 {
        letter-spacing: 0em;
      }

      .cls-12 {
        letter-spacing: .04em;
      }

      .cls-13 {
        fill: url(#linear-gradient);
      }

      .cls-5 {
        font-size: 316.99px;
      }
    </style>
    <linearGradient id="linear-gradient" x1="315.65" y1="719.42" x2="315.65" y2="166.31" gradientUnits="userSpaceOnUse">
      <stop offset=".38" stop-color="#357480"/>
      <stop offset=".49" stop-color="#387d7f"/>
      <stop offset=".55" stop-color="#3b857f"/>
      <stop offset=".85" stop-color="#67b798"/>
      <stop offset="1" stop-color="#a0d677"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="527.16" y1="557.26" x2="527.16" y2="60.02" gradientUnits="userSpaceOnUse">
      <stop offset=".38" stop-color="#357480"/>
      <stop offset=".49" stop-color="#387d7f"/>
      <stop offset=".55" stop-color="#3b857f"/>
      <stop offset=".83" stop-color="#67b798"/>
      <stop offset=".99" stop-color="#a0d678"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="-103.66" y1="557.21" x2="-103.66" y2="59.97" gradientTransform="translate(79.87) rotate(-180) scale(1 -1)" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-4" x1="-250.24" y1="392.68" x2="-250.24" y2="0" gradientTransform="translate(106.76) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".46" stop-color="#357480"/>
      <stop offset=".55" stop-color="#387d7f"/>
      <stop offset=".6" stop-color="#3b857f"/>
      <stop offset=".85" stop-color="#67b798"/>
      <stop offset="1" stop-color="#a5d485"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="355.19" y1="725.8" x2="355.19" y2="164.18" gradientUnits="userSpaceOnUse">
      <stop offset=".04" stop-color="#358c8c"/>
      <stop offset=".12" stop-color="#3b958e"/>
      <stop offset=".22" stop-color="#48a694"/>
      <stop offset=".44" stop-color="#5bb299"/>
      <stop offset=".79" stop-color="#b2df6d"/>
    </linearGradient>
  </defs>
  <g id="Layer_1-2" data-name="Layer 1">
    <g id="_0" data-name="0">
      <g id="Layer_60" data-name="Layer 60">
        <path class="cls-13" d="M619.4,545.23c-6.65,7.4-13.57,14.54-20.7,21.37-74.17,70.98-169.86,114.66-265.17,152.83-24.37-19.75-51.44-36.18-80.22-48.68-24.39-10.59-50.05-18.41-73.36-31.2-28.65-15.73-52.79-38.45-75.4-62.05-32.78-34.22-63.73-71.98-78.76-116.92-8.48-25.35-11.61-52.18-13.03-78.87-1.83-34.27-.94-68.77,4.49-102.66,6.49-40.53,99.45-22.03,106.17-62.52,6.72-40.49,33.94,19.84,15.88-17.02,31.18-6.07-13.84-26.43,10.21-5.67,56.15,48.45,17.88-51.85,39.3-21.79"/>
        <path class="cls-1" d="M345.93,382c-9.35-33.89,170.66-277.56,198.81-298.62,41.69-31.2,87.02-21.78,122.17-22.73l13.91,1.35c-22.91,36.14-20.89,82.88-10.42,124.37,10.47,41.49,28.5,81.04,35.56,123.24,16.92,101.26-45.66,176.67-119.84,247.66"/>
        <path class="cls-7" d="M364.76,381.95c9.35-33.89-170.66-277.56-198.81-298.62-41.69-31.2-87.02-21.78-122.17-22.73l-13.91,1.35c22.91,36.14,20.89,82.88,10.42,124.37-10.47,41.49-28.5,81.04-35.56,123.24-16.92,101.26,45.66,176.67,119.84,247.66"/>
        <path class="cls-6" d="M357.16,0c-6.16.36-11.38,3.77-16.16,7.05-40.62,27.95-79.96,57.8-110.85,93.41-30.88,35.61-53.07,77.55-55.86,121.48-12.24,84.14,150.98,171.54,182.73,170.72.24,0,.47-.01.71-.02h-1.46c.24,0,.47.02.71.02,31.75.82,194.97-86.58,182.73-170.72-2.8-43.93-24.98-85.87-55.86-121.48-30.88-35.61-70.23-65.46-110.85-93.41-4.77-3.28-9.99-6.69-16.16-7.05"/>
        <rect class="cls-3" x="295.58" y="136.09" width="123.49" height="398.28" rx="1" ry="1" transform="translate(22.1 692.55) rotate(-90)"/>
        <rect class="cls-3" x="294.52" y="136.09" width="125.61" height="398.28" rx="1" ry="1"/>
        <path class="cls-8" d="M42.8,176.84s-10.72,42.94-4.39,87.18c6.44,44.99,21.86,102.23,89.82,152.01,131.27,96.14,304,93.59,418.01,6.81,97.65-74.34,118.86-258.65,118.86-258.65,0,0,27.42,88.08,27.53,88.5,18.14,71.68,39.52,147.45-31.95,253.96-53.19,79.27-113.86,122.89-153.3,146.19-32.37,19.12-150,72.97-150,72.97,0,0-57.75-13.18-100.46-29.44-69.67-26.52-98.48-47.1-120.48-61.68-87.04-57.72-119.18-140.38-124.22-159.95C-5.61,405.49.54,347.25,3.29,316.2c4.63-52.16,39.75-141.24,39.51-139.36Z"/>
      </g>
    </g>
    <text class="cls-4" transform="translate(853.74 311.13) scale(.98 1)"><tspan class="cls-11" x="0" y="0">self-</tspan><tspan class="cls-10" x="844.52" y="0">p</tspan><tspan class="cls-2" x="1011.62" y="0">a</tspan><tspan class="cls-11" x="1189.81" y="0" xml:space="preserve">y   </tspan></text>
    <text class="cls-5" transform="translate(831.96 637.15) scale(1.1 1)"><tspan class="cls-12" x="0" y="0">HEA</tspan><tspan class="cls-9" x="685.65" y="0">L</tspan><tspan class="cls-12" x="831.46" y="0">TH</tspan></text>
  </g>
</svg>