'use client';

import ResultsSection from '../../components/search/ResultsSection';
import { useState } from 'react';
import { Container, Box, Typography } from '@mui/material';
import { AIChat } from '../../components/chat-bot/AiChat';
import TextSection from '../../components/search/TextSection';
import { Clinic } from '../../types/clinic.type';
import { getClinics } from '../../lib/api/clinics';
import { SearchTerm } from '../../lib/api/types/search-term.type';
import { SearchProvider } from '../../contexts/search-context';
import SearchBarDesktop from '../../components/search/SearchBar';
import { IsMobile } from '../../lib/utils';

const Loading = () => {
  return (
    <Typography variant="h5" mb={1}>
      Searching...
    </Typography>
  );
};

function SearchPageClient() {
  const [results, setResults] = useState<Clinic[]>([]);
  const [loading, setLoading] = useState(false);
  const [aiSelected, setAiSelected] = useState(false);
  const [firstSearchComplete, setFirstSearchComplete] = useState(false);

  const handleSearch = async ({
    searchTerm,
    postcode,
    distance,
  }: {
    searchTerm: SearchTerm;
    postcode: string | undefined;
    distance: number | undefined;
  }) => {
    setLoading(true);

    const clinics = await getClinics({
      id: searchTerm.id,
      type: searchTerm.type,
      postcode,
      distance,
    });

    setResults(clinics);
    setLoading(false);
    setFirstSearchComplete(true);
    window.dataLayer = window.dataLayer || [];
    window.dataLayer.push({
      event: 'search_results_displayed',
      search_term: searchTerm.name,
      search_term_id: searchTerm.id,
      search_term_type: searchTerm.type,
      results_count: clinics.length,
    });

    window.dataLayer.push({
      event: 'view_item_list',
      search_term: searchTerm.name,
      search_term_id: searchTerm.id,
      search_term_type: searchTerm.type,
      item_list_name: 'Search Results',
      items: clinics.map((clinic, index) => ({
        item_id: clinic.id,
        item_name: clinic.name,
        index: index + 1,
        rating: clinic.rating || null,
      })),
    });
  };

  console.log(results);

  return (
    <SearchProvider>
      <Container maxWidth="lg" sx={{ mt: { xs: 6, md: 8 } }}>
        <TextSection aiSelected={aiSelected} setAiSelected={setAiSelected} />
        {aiSelected ? (
          <Box mt={6}>
            <AIChat handleSearch={handleSearch} />
          </Box>
        ) : (
          <Box width="100%" mt={6}>
            <SearchBarDesktop handleSearch={handleSearch} />
          </Box>
        )}
        <Box my={IsMobile() ? 2 : 4}>
          {loading ? (
            <Loading />
          ) : firstSearchComplete ? (
            <ResultsSection results={results} />
          ) : null}
        </Box>
      </Container>
    </SearchProvider>
  );
}

// Server component wrapper for metadata
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Search Private Healthcare - Self Pay Health',
  description:
    'Search and compare private healthcare treatments, clinics, and specialists across the UK. Find the best prices and book appointments with trusted providers.',
  keywords: [
    'search healthcare',
    'private treatment search',
    'find clinics',
    'healthcare comparison',
    'medical search',
    'private healthcare UK',
  ],
  openGraph: {
    title: 'Search Private Healthcare - Self Pay Health',
    description:
      'Search and compare private healthcare treatments, clinics, and specialists across the UK. Find the best prices and book appointments with trusted providers.',
    type: 'website',
    url: `${process.env.NEXT_PUBLIC_SITE_URL}/search`,
    siteName: 'Self Pay Health',
    images: [
      {
        url: `${process.env.NEXT_PUBLIC_SITE_URL}/og-image.jpg`,
        width: 1200,
        height: 630,
        alt: 'Self Pay Health - Search Private Healthcare',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Search Private Healthcare - Self Pay Health',
    description:
      'Search and compare private healthcare treatments, clinics, and specialists across the UK. Find the best prices and book appointments with trusted providers.',
  },
  robots: {
    index: true,
    follow: true,
  },
  alternates: {
    canonical: `${process.env.NEXT_PUBLIC_SITE_URL}/search`,
  },
};

export default function SearchPage() {
  return <SearchPageClient />;
}
