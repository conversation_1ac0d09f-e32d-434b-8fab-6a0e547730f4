'use client';

import ResultsSection from '../../components/search/ResultsSection';
import { useState } from 'react';
import { Container, Box, Typography } from '@mui/material';
import { AIChat } from '../../components/chat-bot/AiChat';
import TextSection from '../../components/search/TextSection';
import { Clinic } from '../../types/clinic.type';
import { getClinics } from '../../lib/api/clinics';
import { SearchTerm } from '../../lib/api/types/search-term.type';
import { SearchProvider } from '../../contexts/search-context';
import SearchBarDesktop from '../../components/search/SearchBar';
import { IsMobile } from '../../lib/utils';

const Loading = () => {
  return (
    <Typography variant="h5" mb={1}>
      Searching...
    </Typography>
  );
};

export default function SearchPage() {
  const [results, setResults] = useState<Clinic[]>([]);
  const [loading, setLoading] = useState(false);
  const [aiSelected, setAiSelected] = useState(false);
  const [firstSearchComplete, setFirstSearchComplete] = useState(false);

  const handleSearch = async ({
    searchTerm,
    postcode,
    distance,
  }: {
    searchTerm: SearchTerm;
    postcode: string | undefined;
    distance: number | undefined;
  }) => {
    setLoading(true);

    const clinics = await getClinics({
      id: searchTerm.id,
      type: searchTerm.type,
      postcode,
      distance,
    });

    setResults(clinics);
    setLoading(false);
    setFirstSearchComplete(true);
    window.dataLayer = window.dataLayer || [];
    window.dataLayer.push({
      event: 'search_results_displayed',
      search_term: searchTerm.name,
      search_term_id: searchTerm.id,
      search_term_type: searchTerm.type,
      results_count: clinics.length,
    });

    window.dataLayer.push({
      event: 'view_item_list',
      search_term: searchTerm.name,
      search_term_id: searchTerm.id,
      search_term_type: searchTerm.type,
      item_list_name: 'Search Results',
      items: clinics.map((clinic, index) => ({
        item_id: clinic.id,
        item_name: clinic.name,
        index: index + 1,
        rating: clinic.rating || null,
      })),
    });
  };

  console.log(results);

  return (
    <SearchProvider>
      <Container maxWidth="lg" sx={{ mt: { xs: 6, md: 8 } }}>
        <TextSection aiSelected={aiSelected} setAiSelected={setAiSelected} />
        {aiSelected ? (
          <Box mt={6}>
            <AIChat handleSearch={handleSearch} />
          </Box>
        ) : (
          <Box width="100%" mt={6}>
            <SearchBarDesktop handleSearch={handleSearch} />
          </Box>
        )}
        <Box my={IsMobile() ? 2 : 4}>
          {loading ? (
            <Loading />
          ) : firstSearchComplete ? (
            <ResultsSection results={results} />
          ) : null}
        </Box>
      </Container>
    </SearchProvider>
  );
}
