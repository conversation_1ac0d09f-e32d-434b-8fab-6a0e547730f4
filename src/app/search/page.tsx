import type { Metadata } from 'next';
import SearchPageClient from './SearchPageClient';

export const metadata: Metadata = {
  title: 'Search Private Healthcare - Self Pay Health',
  description:
    'Search and compare private healthcare treatments, clinics, and specialists across the UK. Find the best prices and book appointments with trusted providers.',
  keywords: [
    'search healthcare',
    'private treatment search',
    'find clinics',
    'healthcare comparison',
    'medical search',
    'private healthcare UK',
  ],
  openGraph: {
    title: 'Search Private Healthcare - Self Pay Health',
    description:
      'Search and compare private healthcare treatments, clinics, and specialists across the UK. Find the best prices and book appointments with trusted providers.',
    type: 'website',
    url: `${process.env.NEXT_PUBLIC_SITE_URL}/search`,
    siteName: 'Self Pay Health',
    images: [
      {
        url: `${process.env.NEXT_PUBLIC_SITE_URL}/og-image.jpg`,
        width: 1200,
        height: 630,
        alt: 'Self Pay Health - Search Private Healthcare',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Search Private Healthcare - Self Pay Health',
    description:
      'Search and compare private healthcare treatments, clinics, and specialists across the UK. Find the best prices and book appointments with trusted providers.',
  },
  robots: {
    index: true,
    follow: true,
  },
  alternates: {
    canonical: `${process.env.NEXT_PUBLIC_SITE_URL}/search`,
  },
};

export default function SearchPage() {
  return <SearchPageClient />;
}
