'use client';

import ResultsSection from '../../components/search/ResultsSection';
import { useState } from 'react';
import { Container, Box, Typography } from '@mui/material';
import { AIChat } from '../../components/chat-bot/AiChat';
import TextSection from '../../components/search/TextSection';
import { Clinic } from '../../types/clinic.type';
import { getClinics } from '../../lib/api/clinics';
import { SearchTerm } from '../../lib/api/types/search-term.type';
import { SearchProvider } from '../../contexts/search-context';
import SearchBar from '../../components/search/SearchBar';
import { IsMobile } from '../../lib/utils';
import ClinicModal from '@/components/clinic-modal/ClinicModal';
import { useClinicModal } from '@/hooks/use-clinic-modal';
import { SearchBarParams } from '@/lib/api/types/searchbar-params.type';
import { trackSearch, trackViewItemList } from '../../utils/tracking';

const Loading = () => {
  return (
    <Typography variant="h5" mb={1}>
      Searching...
    </Typography>
  );
};

export default function SearchPageClient() {
  const [clinics, setClinics] = useState<Clinic[]>([]);
  const [loading, setLoading] = useState(false);
  const [aiSelected, setAiSelected] = useState(false);
  const [firstSearchComplete, setFirstSearchComplete] = useState(false);
  const [searchParams, setSearchParams] = useState<SearchBarParams>({
    searchTerm: undefined,
    postcode: undefined,
    distance: undefined,
  });

  const { selectedClinic, selectedIndex, closeModal, isOpen, openModal } =
    useClinicModal();

  const handleSearch = async ({
    id,
    type,
    searchTerm,
    postcode,
    distance,
    searchSource = 'manual',
  }: {
    id?: string | undefined;
    type?: string | undefined;
    searchTerm: SearchTerm;
    postcode: string | undefined;
    distance: number | undefined;
    searchSource?: 'manual' | 'ai';
  }) => {
    setLoading(true);
    // console.log('called handle search');
    // console.log(searchSource);
    // TODO - check this implementation to see if can make better

    let clinics: Clinic[] = [];

    if (searchSource === 'manual') {
      // console.log('call fired within manual check if statement');
      clinics = await getClinics({
        id: searchTerm.id,
        type: searchTerm.type,
        searchTerm: searchTerm.name,
        postcode,
        distance,
      });
    }
    if (searchSource === 'ai') {
      // console.log('call fired within ai check if statement');
      clinics = await getClinics({
        searchTerm: searchTerm as unknown as string,
        id,
        type,
        postcode,
        distance: 20 * 1609.34,
      });
    }

    setSearchParams({ searchTerm, postcode, distance });

    setClinics(clinics);
    setLoading(false);
    setFirstSearchComplete(true);

    trackSearch({
      searchTerm,
      searchSource,
      resultsCount: clinics.length,
      postcode,
      distance,
    });

    // Track view item list with user ID
    trackViewItemList({
      searchTerm,
      searchSource,
      postcode,
      distance,
      items: clinics.map((clinic, index) => ({
        id: clinic.id,
        name: clinic.name,
        index: index + 1,
        rating: clinic.rating || null,
        quantity: 1,
      })),
    });
  };

  return (
    <SearchProvider>
      <Container maxWidth="lg" sx={{ mt: { xs: 6, md: 8 } }}>
        <TextSection aiSelected={aiSelected} setAiSelected={setAiSelected} />
        {aiSelected ? (
          <Box mt={6}>
            <AIChat
              handleSearch={(params) =>
                handleSearch({ ...params, searchSource: 'ai' })
              }
            />
          </Box>
        ) : (
          <Box width="100%" mt={6}>
            {/* <SearchBarDesktop handleSearch={handleSearch} /> */}
            <SearchBar
              handleSearch={(params) =>
                handleSearch({ ...params, searchSource: 'manual' })
              }
            />
          </Box>
        )}
        <Box my={IsMobile() ? 2 : 4}>
          {loading ? (
            <Loading />
          ) : firstSearchComplete ? (
            <>
              <ResultsSection
                clinics={clinics}
                onViewClinicDetails={openModal}
                searchParams={searchParams}
                searchSource={aiSelected ? 'ai' : 'manual'}
              />
            </>
          ) : null}
        </Box>
      </Container>
      {selectedClinic && (
        <ClinicModal
          searchParams={searchParams}
          index={selectedIndex}
          clinic={selectedClinic}
          open={isOpen}
          onClose={closeModal}
          searchSource={aiSelected ? 'ai' : 'manual'}
        />
      )}
    </SearchProvider>
  );
}
