/* eslint-disable react/jsx-no-comment-textnodes */
import { ThemeProvider } from '../ui/ThemeProvider';
import { AppRouterCacheProvider } from '@mui/material-nextjs/v15-appRouter';
import { Poppins } from 'next/font/google';
import type { Metadata } from 'next';
import '../ui/globals.css';
import Navbar from '../shared/components/Navbar';
import Footer from '../shared/components/Footer';
import Script from 'next/script';
import { GoogleTagManager } from '@next/third-parties/google';
import UserIdProvider from '../components/UserIdProvider'; // Add this import

const poppins = Poppins({
  variable: '--font-poppins',
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700'],
});

export const metadata: Metadata = {
  title: {
    default: 'Self Pay Health - Private Healthcare Platform',
    template: '%s | Self Pay Health',
  },
  description:
    'Find and book private healthcare treatments across the UK. Compare prices, read reviews, and book appointments with trusted private clinics and specialists.',
  metadataBase: new URL(
    process.env.NEXT_PUBLIC_SITE_URL || 'https://selfpayhealth.co.uk'
  ),
  icons: {
    icon: 'images/logos/only-flower.svg',
    shortcut: 'images/logos/only-flower.svg',
    apple: 'images/logos/only-flower.svg',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        {process.env.NODE_ENV !== 'development' && (
          <Script src="https://consent.cookiefirst.com/sites/selfpayhealth.co.uk-27f3e2d7-caec-4c0e-baca-6bca14d91f2a/consent.js" />
        )}

        <script
          dangerouslySetInnerHTML={{
            __html: `
!function(f,b,e,v,n,t,s)
{if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};
if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];
s.parentNode.insertBefore(t,s)}(window, document,'script',
'https://connect.facebook.net/en_US/fbevents.js');
fbq('init', '795732126367113');
fbq('track', 'PageView');
`,
          }}
        />
        <noscript>
          // eslint-disable-next-line @next/next/no-img-element,
          @next/next/no-img-element
          <img
            height="1"
            width="1"
            style={{ display: 'none' }}
            src="https://www.facebook.com/tr?id=795732126367113&ev=PageView&noscript=1"
          />
        </noscript>
      </head>
      {process.env.NODE_ENV !== 'development' && (
        <GoogleTagManager
          gtmId="GTM-MH7W9T64"
          auth={process.env.NEXT_PUBLIC_GTM_AUTH}
          preview={process.env.NEXT_PUBLIC_GTM_PREVIEW}
        />
      )}
      <body className={poppins.variable} suppressHydrationWarning>
        <AppRouterCacheProvider>
          <UserIdProvider />
          <ThemeProvider>
            <main
              style={{
                display: 'flex',
                flexDirection: 'column',
                minHeight: '100vh',
              }}
            >
              <Navbar />
              <div
                style={{ flex: '1 0 auto', minHeight: 'calc(100vh - 80px)' }}
              >
                {children}
              </div>
              <Footer />
            </main>
          </ThemeProvider>
        </AppRouterCacheProvider>
      </body>
    </html>
  );
}
