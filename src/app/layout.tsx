import { ThemeProvider } from '../ThemeProvider';
import { Poppins } from 'next/font/google';
import type { Metadata } from 'next';
import '../globals.css';
import Navbar from '../shared/components/Navbar';
import Footer from '../shared/components/Footer';
import Script from 'next/script';
import { GoogleTagManager } from '@next/third-parties/google';

const poppins = Poppins({
  variable: '--font-poppins',
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700'],
});

export const metadata: Metadata = {
  title: 'Create Next App',
  description: 'Generated by create next app',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        {process.env.NODE_ENV !== 'development' && (
          <Script src="https://consent.cookiefirst.com/sites/selfpayhealth.co.uk-27f3e2d7-caec-4c0e-baca-6bca14d91f2a/consent.js" />
        )}
      </head>
      {process.env.NODE_ENV !== 'development' && (
        <GoogleTagManager
          gtmId="GTM-MH7W9T64"
          auth={process.env.NEXT_PUBLIC_GTM_AUTH}
          preview={process.env.NEXT_PUBLIC_GTM_PREVIEW}
        />
      )}
      <body className={poppins.variable} suppressHydrationWarning>
        <ThemeProvider>
          <main
            style={{
              display: 'flex',
              flexDirection: 'column',
              minHeight: '100vh',
            }}
          >
            <Navbar />
            <div style={{ flex: '1 0 auto', minHeight: 'calc(100vh - 80px)' }}>
              {children}
            </div>
            <Footer />
          </main>
        </ThemeProvider>
      </body>
    </html>
  );
}
