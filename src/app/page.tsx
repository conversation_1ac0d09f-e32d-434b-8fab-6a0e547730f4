import { Container, Typography, Paper } from '@mui/material';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Self Pay Health - Private Healthcare Search & Booking Platform',
  description:
    'Find and book private healthcare treatments across the UK. Compare prices, read reviews, and book appointments with trusted private clinics and specialists.',
  keywords: [
    'private healthcare',
    'private clinics',
    'healthcare booking',
    'medical treatments',
    'UK healthcare',
    'self pay health',
  ],
  openGraph: {
    title: 'Self Pay Health - Private Healthcare Search & Booking Platform',
    description:
      'Find and book private healthcare treatments across the UK. Compare prices, read reviews, and book appointments with trusted private clinics and specialists.',
    type: 'website',
    url: `${process.env.NEXT_PUBLIC_SITE_URL}`,
    siteName: 'Self Pay Health',
    images: [
      {
        url: `${process.env.NEXT_PUBLIC_SITE_URL}/og-image.jpg`, // You'll need to add this image
        width: 1200,
        height: 630,
        alt: 'Self Pay Health - Private Healthcare Platform',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Self Pay Health - Private Healthcare Search & Booking Platform',
    description:
      'Find and book private healthcare treatments across the UK. Compare prices, read reviews, and book appointments with trusted private clinics and specialists.',
  },
  robots: {
    index: true,
    follow: true,
  },
  alternates: {
    canonical: `${process.env.NEXT_PUBLIC_SITE_URL}`,
  },
};

export default function HomePage() {
  return (
    <Container
      sx={{
        textAlign: 'center',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        height: '80vh',
      }}
    >
      <Paper elevation={0} sx={{ p: { xs: 3, md: 6 }, borderRadius: 2 }}>
        <Typography variant="body1" component="p" gutterBottom>
          Self Pay Health is under construction
        </Typography>
      </Paper>
    </Container>
  );
}
