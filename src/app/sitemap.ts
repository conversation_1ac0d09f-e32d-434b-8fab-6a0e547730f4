import { MetadataRoute } from 'next';
import { getPathsForSitemap } from '@/lib/sanity/sanity';

interface SitemapPath {
  href: string;
  _updatedAt: string;
}

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://selfpayhealth.com';

    // Define static pages with their last modified dates and priorities
    const staticPages = [
      {
        url: new URL('/', baseUrl).toString(),
        lastModified: new Date(),
        changeFrequency: 'monthly' as const,
        priority: 1
      },
      {
        url: new URL('/search', baseUrl).toString(), // Update this when renamed to /search
        lastModified: new Date(),
        changeFrequency: 'monthly' as const,
        priority: 0.8
      },
      {
        url: new URL('/terms', baseUrl).toString(),
        lastModified: new Date(),
        changeFrequency: 'yearly' as const,
        priority: 0.5
      },
      {
        url: new URL('/privacy-policy', baseUrl).toString(),
        lastModified: new Date(),
        changeFrequency: 'yearly' as const,
        priority: 0.5
      }
      // Add any other static pages here
    ];

    // Get dynamic content pages from Sanity
    const paths = await getPathsForSitemap();

    if (!paths) return staticPages;

    // Combine static and dynamic pages
    const dynamicPages = paths.map((path: SitemapPath) => ({
      url: new URL(path.href!, baseUrl).toString(),
      lastModified: new Date(path._updatedAt),
      changeFrequency: 'weekly' as const,
      priority: 0.7
    }));

    return [...staticPages, ...dynamicPages];
  } catch (error) {
    console.error('Failed to generate sitemap:', error);
    return [];
  }
}
