import { Box, Button, Paper, Typography } from '@mui/material';
import { ContentTemplateContent } from '../../../../types/content.types';
import { ContentHeader } from '@/components/content/ContentHeader';
import { SectionComponentMap } from '@/components/content';
import React from 'react';
import { Divider } from '@/components/common/Divider';
import ArrowCircleRightIcon from '@mui/icons-material/ArrowCircleRight';
import link from 'next/link';

export function ContentTemplate({
  content,
}: {
  content: ContentTemplateContent;
}) {
  return (
    <Box
      component="article"
      sx={{ maxWidth: '1100px', mx: 'auto', py: 4, px: 2 }}
    >
      <ContentHeader content={content} />

      <Box className="content-blocks">
        {content.contentBlocks &&
          content.contentBlocks.map((block) => {
            const SectionComponent = SectionComponentMap[block._type];

            return (
              <Box key={block._key}>
                {SectionComponent ? (
                  <SectionComponent block={block} />
                ) : (
                  <Paper
                    sx={{
                      p: 2,
                      mb: 2,
                      border: 1,
                      borderColor: 'warning.light',
                      bgcolor: 'warning.lighter',
                      borderRadius: 1,
                    }}
                  >
                    <Typography>Unknown block type: {block._type}</Typography>
                  </Paper>
                )}

                {block.divider?.includeDivider && (
                  <Divider colorScheme={block.divider.colorScheme} />
                )}
              </Box>
            );
          })}
      </Box>
      <Box mt="5rem" mb="1rem" justifyContent="center" display="flex">
        <Button
          LinkComponent={link}
          href="/search"
          size="large"
          className="primary-button"
          endIcon={
            <ArrowCircleRightIcon
              sx={{
                fontSize: '2.5rem !important',
                textTransform: 'none',
              }}
            />
          }
        >
          <Typography variant="body1" textTransform="none" fontWeight={500}>
            Go to Search
          </Typography>
        </Button>
      </Box>
    </Box>
  );
}
