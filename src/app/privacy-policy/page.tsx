import {
  Box,
  Container,
  Typography,
  Paper,
  Divider,
  List,
  ListItem,
} from '@mui/material';

export default function PrivacyPolicyPage() {
  return (
    <Container maxWidth="lg" sx={{ py: 6 }}>
      <Paper elevation={0} sx={{ p: { xs: 3, md: 6 }, borderRadius: 2 }}>
        <Typography variant="h2" component="h1" gutterBottom>
          PRIVACY POLICY
        </Typography>

        <Section title="Introduction">
          <Typography variant="body1">
            Self Pay Health is a service operated by Dodec Ltd (&quot;we&quot;,
            &quot;us&quot;, &quot;our&quot;). This Privacy Policy explains how
            we collect, use, and protect your personal information when you use
            our website www.selfpayhealth.co.uk (&quot;Website&quot;) and the
            services provided through it (&quot;Services&quot;).
          </Typography>
          <Typography variant="body1">
            We are committed to ensuring that your privacy is protected. We
            comply with the UK General Data Protection Regulation (UK GDPR) and
            the Data Protection Act 2018.
          </Typography>
        </Section>

        <Section title="Who We Are">
          <Typography variant="body1">
            Self Pay Health is operated by Dodec Ltd, a company registered in
            England and Wales. For data protection purposes, Dodec Ltd is the
            data controller.
          </Typography>
        </Section>

        <Section title="Information We Collect">
          <Typography variant="body1">
            We may collect the following types of information:
          </Typography>
          <SubSection title="Personal Information">
            <BulletList
              items={[
                'Name and contact details (email address, phone number, postal address)',
                'Date of birth',
                'Gender',
                'Information about your health and medical conditions that you choose to share with us',
                'Payment information (though we do not store complete payment card details)',
              ]}
            />
          </SubSection>
          <SubSection title="Technical Information">
            <BulletList
              items={[
                'IP address',
                'Browser type and version',
                'Operating system',
                'Referral source',
                'Length of visit, page views, website navigation',
                'Other information collected through cookies and similar technologies',
              ]}
            />
          </SubSection>
        </Section>

        <Section title="How We Collect Your Information">
          <Typography variant="body1">
            We collect information through:
          </Typography>
          <BulletList
            items={[
              'Forms you complete on our Website',
              'Correspondence with us by phone, email, or otherwise',
              'Your use of our Website, through cookies and similar technologies',
              'Third parties, such as healthcare providers you connect with through our Services (with your consent)',
            ]}
          />
        </Section>

        <Section title="How We Use Your Information">
          <Typography variant="body1">
            We use your information for the following purposes:
          </Typography>
          <BulletList
            items={[
              'To provide our Services to you',
              'To connect you with healthcare providers and facilitate your healthcare arrangements',
              'To process payments',
              'To respond to your inquiries',
              'To improve our Website and Services',
              'To send you information about our Services (if you have consented to this)',
              'To comply with legal and regulatory requirements',
            ]}
          />
        </Section>

        <Section title="Legal Basis for Processing">
          <Typography variant="body1">
            We process your personal information on the following legal bases:
          </Typography>
          <BulletList
            items={[
              'Your consent',
              'Performance of a contract with you',
              'Our legitimate interests (to provide and improve our Services)',
              'Compliance with legal obligations',
            ]}
          />
        </Section>

        <Section title="Special Category Data">
          <Typography variant="body1">
            Health information is considered &quot;special category data&quot;
            under data protection law. We only process such data when:
          </Typography>
          <BulletList
            items={[
              'You have given explicit consent',
              'Processing is necessary for the purposes of preventive or occupational medicine, medical diagnosis, or the provision of health or social care treatment',
              'Processing is necessary to protect your vital interests',
              'Processing is necessary for reasons of substantial public interest',
            ]}
          />
        </Section>

        <Section title="Cookies">
          <Typography variant="body1">
            Our Website uses cookies to distinguish you from other users. This
            helps us provide you with a good experience and allows us to improve
            our Website. For detailed information about the cookies we use and
            how to manage them, please see our Cookie Policy.
          </Typography>
        </Section>

        <Section title="Sharing Your Information">
          <Typography variant="body1">
            We may share your personal information with:
          </Typography>
          <BulletList
            items={[
              'Healthcare providers you choose to connect with through our Services',
              'Service providers who perform functions on our behalf (such as payment processors, IT service providers)',
              'Professional advisers (lawyers, accountants, auditors)',
              'Government bodies and law enforcement agencies, if required by law',
            ]}
          />
          <Typography variant="body1">
            We require all third parties to respect the security of your
            personal information and to treat it in accordance with the law.
          </Typography>
        </Section>

        <Section title="International Transfers">
          <Typography variant="body1">
            We do not transfer your personal data outside the UK and European
            Economic Area (EEA) unless appropriate safeguards are in place.
          </Typography>
        </Section>

        <Section title="Data Security">
          <Typography variant="body1">
            We have implemented appropriate security measures to prevent your
            personal information from being accidentally lost, used, or accessed
            in an unauthorized way, altered, or disclosed. We limit access to
            your personal information to those employees, agents, contractors,
            and other third parties who have a business need to know.
          </Typography>
        </Section>

        <Section title="Data Retention">
          <Typography variant="body1">
            We will only retain your personal information for as long as
            necessary to fulfill the purposes we collected it for, including for
            the purposes of satisfying any legal, accounting, or reporting
            requirements.
          </Typography>
        </Section>

        <Section title="Your Rights">
          <Typography variant="body1">
            Under data protection law, you have rights including:
          </Typography>
          <BulletList
            items={[
              'The right to access your personal information',
              'The right to rectification if your personal information is inaccurate or incomplete',
              'The right to erasure (the "right to be forgotten")',
              'The right to restrict processing of your personal information',
              'The right to data portability',
              'The right to object to processing of your personal information',
              'Rights in relation to automated decision making and profiling',
            ]}
          />
          <Typography variant="body1">
            To exercise any of these rights, please contact us using the details
            provided below.
          </Typography>
        </Section>

        <Section title="Children's Privacy">
          <Typography variant="body1">
            Our Services are not intended for children under 16 years of age,
            and we do not knowingly collect personal information from children
            under 16. If you are under 16, please do not provide any information
            on our Website.
          </Typography>
        </Section>

        <Section title="Changes to This Privacy Policy">
          <Typography variant="body1">
            We may update this Privacy Policy from time to time by posting a new
            version on our Website. We will notify you of any significant
            changes by email or through a notice on our Website.
          </Typography>
        </Section>

        <Section title="Contact Us">
          <Typography variant="body1">
            If you have any questions about this Privacy Policy or our data
            practices, please contact us at:
          </Typography>
          <Typography variant="body1">Email: <EMAIL></Typography>
          <Typography variant="body1">
            Address: Hobland, Sonning Eye, Reading, RG4 6TN
          </Typography>
        </Section>

        <Section title="Complaints">
          <Typography variant="body1">
            If you have a concern about our use of your information, you can
            contact the Information Commissioner&apos;s Office (ICO), the UK
            supervisory authority for data protection issues (www.ico.org.uk).
          </Typography>
        </Section>

        <Box sx={{ mt: 4 }}>
          <Typography variant="body2" color="text.secondary">
            Last updated: 05.05.2025
          </Typography>
        </Box>
      </Paper>
    </Container>
  );
}

// Helper components for consistent styling
function Section({
  title,
  children,
}: {
  title: string;
  children: React.ReactNode;
}) {
  return (
    <Box sx={{ mt: 4, mb: 3 }}>
      <Typography variant="h4" component="h2" gutterBottom>
        {title}
      </Typography>
      <Divider sx={{ mb: 2 }} />
      {children}
    </Box>
  );
}

function SubSection({
  title,
  children,
}: {
  title: string;
  children: React.ReactNode;
}) {
  return (
    <Box sx={{ mt: 2, mb: 2 }}>
      <Typography variant="h5" component="h3" gutterBottom>
        {title}
      </Typography>
      {children}
    </Box>
  );
}

function BulletList({ items }: { items: string[] }) {
  return (
    <List sx={{ pl: 2, listStyleType: 'disc' }}>
      {items.map((item, index) => (
        <ListItem
          key={index}
          sx={{
            display: 'list-item',
            pl: 1,
            py: 0.5,
          }}
        >
          <Typography variant="body1">{item}</Typography>
        </ListItem>
      ))}
    </List>
  );
}
