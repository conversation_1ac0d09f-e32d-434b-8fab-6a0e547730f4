import { Box, Typography, Grid } from '@mui/material';
import { PortableText } from '@portabletext/react';
import { ContentBlock } from '../types';
import { urlForImage } from '@/lib/sanity/sanityImage';
import Image from 'next/image';
import { portableTextComponents } from '@/lib/sanity/portableTextComponents';

export function GeneralWithImageRightSection({ block }: { block: ContentBlock }) {
  return (
    <Box sx={{ mb: 4, mt: 0 }}>
      {block.heading && (
        <Typography variant='h4' component='h2' sx={{ mb: 2 }}>
          {block.heading}
        </Typography>
      )}
      <Grid container spacing={3}>
        <Grid size={{ xs: 12, md: 6 }}>
          {block.content && (
            <Box className='prose' display='flex' flexDirection='column' justifyContent='center' height='100%'>
              <PortableText value={block.content} components={portableTextComponents} />
            </Box>
          )}
        </Grid>
        {block.image && (
          <Grid size={{ xs: 12, md: 6 }}>
            <Box
              sx={{
                position: 'relative',
                height: { xs: '250px', md: '100%' },
                minHeight: { md: '400px' },
                borderRadius: 1,
                overflow: 'hidden'
              }}
            >
              <Image
                src={urlForImage(block.image).width(1200).height(1200).fit('crop').url()}
                alt={block.image.alt || ''}
                fill
                style={{ objectFit: 'cover' }}
              />
            </Box>

            {block.image.caption && (
              <Typography variant='caption' sx={{ mt: 1, display: 'block', alignItems: 'center' }}>
                {block.image.caption}
              </Typography>
            )}
          </Grid>
        )}
      </Grid>
    </Box>
  );
}
