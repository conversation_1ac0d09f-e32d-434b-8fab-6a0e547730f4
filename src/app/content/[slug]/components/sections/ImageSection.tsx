import { Box, Typography } from '@mui/material';
import { ContentBlock } from '../types';
import { urlForImage } from '@/lib/sanity/sanityImage';
import Image from 'next/image';

export function ImageSection({ block }: { block: ContentBlock }) {
  if (!block.image) return null;

  return (
    <Box sx={{ mb: 4 }}>
      {block.heading && (
        <Typography textAlign='center' variant='h4' sx={{ mb: 2 }}>
          {block.heading}
        </Typography>
      )}
      <Box
        sx={{
          position: 'relative',
          width: '100%',
          height: { xs: '300px', md: '800px' },
          borderRadius: 1,
          overflow: 'hidden'
        }}
      >
        <Image
          src={urlForImage(block.image).width(1200).height(1200).fit('crop').url()}
          alt={block.image.alt || ''}
          fill
          style={{ objectFit: 'contain' }}
        />
      </Box>
      {block.image.caption && (
        <Typography variant='caption' sx={{ mt: 1, display: 'block' }}>
          {block.image.caption}
        </Typography>
      )}
      {block.description && (
        <Typography variant='body2' sx={{ mt: 2 }}>
          {block.description}
        </Typography>
      )}
    </Box>
  );
}
