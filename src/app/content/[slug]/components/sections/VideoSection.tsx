import { Box, Typography } from '@mui/material';
import { ContentBlock } from '../types';

export function VideoSection({ block }: { block: ContentBlock }) {
  if (!block.videoUrl) return null;

  // Extract video ID from YouTube or Vimeo URL
  const getEmbedUrl = (url: string) => {
    // YouTube
    const youtubeRegex =
      /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
    const youtubeMatch = url.match(youtubeRegex);

    if (youtubeMatch && youtubeMatch[1]) {
      return `https://www.youtube.com/embed/${youtubeMatch[1]}`;
    }

    // Vimeo
    const vimeoRegex =
      /(?:vimeo\.com\/(?:channels\/(?:\w+\/)?|groups\/[^\/]*\/videos\/|album\/\d+\/video\/|)(\d+)(?:$|\/|\?))/;
    const vimeoMatch = url.match(vimeoRegex);

    if (vimeoMatch && vimeoMatch[1]) {
      return `https://player.vimeo.com/video/${vimeoMatch[1]}`;
    }

    // If no match, return the original URL
    return url;
  };

  const embedUrl = getEmbedUrl(block.videoUrl);

  return (
    <Box sx={{ mb: 4 }}>
      {block.heading && (
        <Typography textAlign="center" variant="h4" sx={{ mb: 2 }}>
          {block.heading}
        </Typography>
      )}
      <Box
        sx={{
          position: 'relative',
          paddingTop: '56.25%', // 16:9 aspect ratio
          width: '100%',
          overflow: 'hidden',
          borderRadius: 1,
        }}
      >
        <iframe
          src={embedUrl}
          title={block.heading || 'Video content'}
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            border: 0,
          }}
        />
      </Box>
      {block.description && (
        <Typography variant="body2" sx={{ mt: 2 }}>
          {block.description}
        </Typography>
      )}
    </Box>
  );
}
