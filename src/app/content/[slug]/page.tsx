import { notFound } from 'next/navigation';
import { templates, type ContentType } from './templates';
import { getAllContentSlugs, getContentBySlug } from '@/lib/sanity/sanity';
import type { Metadata, ResolvingMetadata } from 'next';
import { urlForImage } from '@/lib/sanity/sanityImage';
import { ContentBlock } from './components/types';

export async function generateStaticParams() {
  const slugs = await getAllContentSlugs();
  return slugs.map((slug: string) => ({ slug }));
}

type Props = {
  params: Promise<{ slug: string }>;
};

export async function generateMetadata(
  { params }: Props,
  parent: ResolvingMetadata
): Promise<Metadata> {
  // Get the slug
  const { slug } = await params;

  // Fetch the content
  const content = await getContentBySlug(slug);

  // Return 404 if content doesn't exist
  if (!content || !content._type || !(content._type in templates)) {
    return {};
  }

  // Get the base URL from parent metadata
  const previousImages = (await parent).openGraph?.images || [];

  // Find main image from content blocks
  let mainImage = null;

  if (content.contentBlocks && content.contentBlocks.length > 0) {
    // First try to find an image marked as main
    const mainImageBlocks = content.contentBlocks.filter(
      (block: ContentBlock) => block.image && block.isMainImage === true
    );

    // If multiple main images found, use the first one
    if (mainImageBlocks.length > 0) {
      mainImage = mainImageBlocks[0].image;
    } else {
      // If no image is marked as main, use the first image found
      const firstImageBlock = content.contentBlocks.find(
        (block: ContentBlock) => block.image
      );

      if (firstImageBlock) {
        mainImage = firstImageBlock.image;
      }
    }
  }

  return {
    title: content.meta?.metaTitle || content.title,
    description: content.meta?.metaDescription,
    keywords: content.meta?.metaKeywords,
    openGraph: {
      title: content.meta?.metaTitle || content.title,
      description: content.meta?.metaDescription,
      type: 'article',
      publishedTime: content.publishedAt,
      url: `${process.env.NEXT_PUBLIC_SITE_URL}/content/${slug}`,
      images: mainImage
        ? [
            {
              url: urlForImage(mainImage).width(1200).height(630).url(),
              width: 1200,
              height: 630,
              alt: mainImage.alt || content.title,
            },
            ...previousImages,
          ]
        : previousImages,
    },
    twitter: {
      card: 'summary_large_image',
      title: content.meta?.metaTitle || content.title,
      description: content.meta?.metaDescription,
    },
  };
}

export default async function ArticlePage({
  params,
}: {
  params: Promise<{ slug: string }>;
}) {
  const { slug } = await params;

  const content = await getContentBySlug(slug);

  if (!content) {
    notFound();
  }

  if (!content._type || !(content._type in templates)) {
    notFound();
  }

  const Template = templates[content._type as ContentType];

  return (
    <>
      <Template content={content} />
    </>
  );
}

// Optional: Set revalidation time
export const revalidate = 3600;
