import { Box, Paper, Typography } from '@mui/material';
import { ContentTemplateContent } from '../../../../types/content.types';
import { ContentHeader } from '@/components/content/ContentHeader';
import { SectionComponentMap } from '@/components/content';

export function ContentTemplate({
  content,
}: {
  content: ContentTemplateContent;
}) {
  return (
    <Box
      component="article"
      sx={{ maxWidth: '1100px', mx: 'auto', py: 4, px: 2 }}
    >
      <ContentHeader content={content} />

      <Box className="content-blocks">
        {content.contentBlocks &&
          content.contentBlocks.map((block) => {
            const SectionComponent = SectionComponentMap[block._type];

            if (SectionComponent) {
              return <SectionComponent key={block._key} block={block} />;
            }

            return (
              <Paper
                key={block._key}
                sx={{
                  p: 2,
                  mb: 2,
                  border: 1,
                  borderColor: 'warning.light',
                  bgcolor: 'warning.lighter',
                  borderRadius: 1,
                }}
              >
                <Typography>Unknown block type: {block._type}</Typography>
              </Paper>
            );
          })}
      </Box>
    </Box>
  );
}
