import { MetadataRoute } from 'next';

export default function robots(): MetadataRoute.Robots {
  // Base URL from environment variable
  const baseUrl =
    process.env.NEXT_PUBLIC_SITE_URL || 'https://selfpayhealth.com';

  return {
    rules: {
      userAgent: '*',
      allow: '/',
      disallow: ['/api/', '/admin/', '/content/cataracts-in-adults'],
    },
    sitemap: `${baseUrl}/sitemap.xml`,
  };
}
