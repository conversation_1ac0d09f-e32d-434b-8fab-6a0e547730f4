export type ColorKey =
  | 'white'
  | 'blueGreen'
  | 'darkGreen'
  | 'primary'
  | 'secondary'
  | 'light'
  | 'dark'
  | 'success'
  | 'error'
  | 'warning';

export interface ColorConfig {
  background: string;
  text: string;
}

export const STATIC_COLOR_MAP: Record<ColorKey, ColorConfig> = {
  white: { background: '#ffffff', text: '#1f1f1f' },
  blueGreen: { background: '#0B747F', text: '#ffffff' },
  darkGreen: { background: '#0C4A5F', text: '#ffffff' },
  primary: { background: '#006c66', text: '#ffffff' },
  secondary: { background: '#c2a629', text: '#ffffff' },
  light: { background: '#f5f5f5', text: '#1f1f1f' },
  dark: { background: '#333333', text: '#ffffff' },
  success: { background: '#2e7d32', text: '#ffffff' },
  error: { background: '#d32f2f', text: '#ffffff' },
  warning: { background: '#ed6c02', text: '#ffffff' },
};

export function getStaticColors(colorKey?: string, fallback: ColorKey = 'light'): ColorConfig {
  const key = (colorKey as ColorKey) || fallback;
  return STATIC_COLOR_MAP[key] || STATIC_COLOR_MAP[fallback];
}

// Convenience functions
export function getStaticSectionColors(backgroundColor?: string): ColorConfig {
  return getStaticColors(backgroundColor, 'white');
}

export function getStaticBoxColors(boxBackgroundColor?: string): ColorConfig {
  return getStaticColors(boxBackgroundColor, 'light');
}
