import { Theme } from '@mui/material/styles';

// Unified color key type that includes all possible color options
export type ColorKey =
  | 'white'
  | 'blueGreen'
  | 'darkGreen'
  | 'primary'
  | 'secondary'
  | 'light'
  | 'dark'
  | 'success'
  | 'error'
  | 'warning';

// Legacy type alias for backward compatibility
export type BackgroundColorKey = ColorKey;
export type BoxBackgroundColorKey = ColorKey;

// Color configuration interface
export interface ColorConfig {
  background: string;
  text: string;
}

// Color map type
export type ColorMap = Record<ColorKey, ColorConfig>;

export function getColorMap(theme: Theme): ColorMap {
  return {
    white: {
      background: '#ffffff',
      text: theme.palette.text.primary,
    },
    blueGreen: {
      background: '#0B747F',
      text: '#ffffff',
    },
    darkGreen: {
      background: '#0C4A5F',
      text: '#ffffff',
    },
    primary: {
      background: theme.palette.primary.main,
      text: '#ffffff',
    },
    secondary: {
      background: theme.palette.secondary.main,
      text: '#ffffff',
    },
    light: {
      background: '#f5f5f5',
      text: theme.palette.text.primary,
    },
    dark: {
      background: '#333333',
      text: '#ffffff',
    },
    success: {
      background: theme.palette.success?.main || '#2e7d32',
      text: '#ffffff',
    },
    error: {
      background: theme.palette.error?.main || '#d32f2f',
      text: '#ffffff',
    },
    warning: {
      background: theme.palette.warning?.main || '#ed6c02',
      text: '#ffffff',
    },
  };
}
