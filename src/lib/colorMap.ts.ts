import { Theme } from '@mui/material/styles';

export type BackgroundColorKey =
  | 'primary'
  | 'secondary'
  | 'light'
  | 'dark'
  | 'success'
  | 'error'
  | 'warning';

export function getColorMap(theme: Theme) {
  return {
    primary: {
      background: theme.palette.primary.main,
      text: '#ffffff',
    },
    secondary: {
      background: theme.palette.secondary.main,
      text: '#ffffff',
    },
    light: {
      background: '#f5f5f5',
      text: theme.palette.text.primary,
    },
    dark: {
      background: '#333333',
      text: '#ffffff',
    },
    white: {
      background: '#ffffff',
      text: '#000000',
    },
    blueGreen: {
      background: '#0B747F',
      text: '#ffffff',
    },
    darkGreen: {
      background: '#0C4A5F',
      text: '#ffffff',
    },
    success: {
      background: '#2e7d32',
      text: '#ffffff',
    },
    error: {
      background: '#d32f2f',
      text: '#ffffff',
    },
    warning: {
      background: '#ed6c02',
      text: '#ffffff',
    },
  };
}
