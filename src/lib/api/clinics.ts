import { Clinic } from '../../types/clinic.type';
import axiosInstance from './axios.instance';
import { ClinicsSearchParams } from './types/clinics.type';
import { SearchTerm } from './types/search-term.type';

export const getClinics = async ({
  distance,
  id,
  postcode,
  searchTerm,
  type,
}: ClinicsSearchParams) => {
  const { data } = await axiosInstance.get<Clinic[]>('/clinics', {
    params: {
      distance: distance && distance * 1609.34,
      id,
      postcode,
      searchTerm,
      type,
    },
  });

  return data;
};

export const getClinicsSearchTerms = async () => {
  const { data } = await axiosInstance.get<SearchTerm[]>(
    '/clinics/search-terms'
  );

  return data;
};
