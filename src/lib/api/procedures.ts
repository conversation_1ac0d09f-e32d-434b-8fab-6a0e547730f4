import { Procedure } from '../../types/procedure.type';
import axiosInstance from './axios.instance';
import { ProceduresSearchParams } from './types/procedures.type';

export const getProcedures = async ({ searchTerm, postcode, distance }: ProceduresSearchParams) => {
  const { data } = await axiosInstance.get<Procedure[]>('/procedures', {
    params: {
      searchTerm,
      postcode,
      distance
    }
  });

  return data;
};
