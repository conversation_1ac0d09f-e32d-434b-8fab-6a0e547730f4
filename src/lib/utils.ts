import { useMediaQuery } from '@mui/material';

export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

export function IsMobile() {
  return useMediaQuery('(max-width: 768px)', { noSsr: true });
}


export function IsTablet() {
  return useMediaQuery('(max-width: 900px)', { noSsr: true });
}
