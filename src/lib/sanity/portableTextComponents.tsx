import { Typography, Link as <PERSON><PERSON><PERSON>ink, Box } from '@mui/material';
import Link from 'next/link';
import Image from 'next/image';
import { urlForImage } from './sanityImage';
import {
  PortableTextReactComponents,
  PortableTextMarkComponentProps,
} from '@portabletext/react';

export const portableTextComponents: Partial<PortableTextReactComponents> = {
  block: {
    h1: ({ children }) => (
      <Typography variant="h3" component="h2" sx={{ mb: 2, mt: 4 }}>
        {children}
      </Typography>
    ),
    h2: ({ children }) => (
      <Typography variant="h4" component="h3" sx={{ mb: 2, mt: 3 }}>
        {children}
      </Typography>
    ),
    h3: ({ children }) => (
      <Typography variant="h5" component="h4" sx={{ mb: 1.5, mt: 2.5 }}>
        {children}
      </Typography>
    ),
    h4: ({ children }) => (
      <Typography variant="h6" component="h5" sx={{ mb: 1, mt: 2 }}>
        {children}
      </Typography>
    ),
    normal: ({ children }) => (
      <Typography variant="body1" sx={{ mb: 2 }}>
        {children}
      </Typography>
    ),
    blockquote: ({ children }) => (
      <Box
        component="blockquote"
        sx={{
          borderLeft: 4,
          borderColor: 'primary.main',
          pl: 2,
          py: 1,
          my: 2,
          fontStyle: 'italic',
        }}
      >
        <Typography variant="body1">{children}</Typography>
      </Box>
    ),
  },
  marks: {
    link: ({
      children,
      value,
    }: PortableTextMarkComponentProps<{ _type: string; href: string }>) => {
      const href = value?.href || '';
      const isExternal = href.startsWith('http');
      return isExternal ? (
        <MuiLink href={href} target="_blank" rel="noopener noreferrer">
          {children}
        </MuiLink>
      ) : (
        <Link href={href} passHref legacyBehavior>
          <MuiLink>{children}</MuiLink>
        </Link>
      );
    },
  },
  types: {
    image: ({ value }) => (
      <Box sx={{ my: 3, position: 'relative', height: '400px', width: '100%' }}>
        <Image
          src={urlForImage(value).width(800).height(400).url()}
          alt={value.alt || ''}
          fill
          style={{ objectFit: 'contain' }}
        />
        {value.caption && (
          <Typography variant="caption" sx={{ mt: 1, display: 'block' }}>
            {value.caption}
          </Typography>
        )}
      </Box>
    ),
  },
};
