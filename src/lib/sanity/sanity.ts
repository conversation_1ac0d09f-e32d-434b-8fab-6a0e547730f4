import { createClient } from 'next-sanity';
import { ContentTemplateContent } from '@/types/content.types';

export interface SitemapPath {
  href: string;
  _updatedAt: string;
}

export const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
  apiVersion: '2023-05-03', // Use the latest API version
  useCdn: process.env.NODE_ENV === 'production', // Use CDN in production
});

export async function getContentBySlug(
  slug: string
): Promise<ContentTemplateContent> {
  return client.fetch(`*[_type in ["content"] && slug.current == $slug][0]`, {
    slug,
  });
}

export async function getAllContentSlugs(): Promise<string[]> {
  return client.fetch(`*[_type in ["content"]].slug.current`);
}

export async function getPathsForSitemap(): Promise<SitemapPath[]> {
  return client.fetch(`*[_type in ["content"] && defined(slug.current)] {
      "href": select(
        _type == "content" => "/content/" + slug.current,
        slug.current
      ),
      _updatedAt
  }
  `);
}
