import { SxProps, Theme } from "@mui/material";

export function getImageLayout(
  layout: string = "default",
  aspectRatio: number = 16 / 9
): SxProps<Theme> {
  if (layout === "fullWidth") {
    return {
      width: "100vw",
      position: "relative",
      left: "50%",
      right: "50%",
      marginLeft: "-50vw",
      marginRight: "-50vw",
      aspectRatio,
      overflow: "hidden",
      borderRadius: 1,
    };
  }

  if (layout === "centered") {
    return {
      mx: "auto",
      width: "100%",
      maxWidth: "900px",
      position: "relative",
      aspectRatio,
      overflow: "hidden",
      borderRadius: 1,
    };
  }

  return {
    position: "relative",
    width: "100%",
    aspectRatio,
    borderRadius: "3px",
    overflow: "hidden",
  };
}
