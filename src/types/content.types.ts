import { PortableTextBlock } from "@portabletext/types";

export interface ContentTemplateContent {
  title: string;
  contentBlocks: ContentBlock[];
  meta: {
    metaTitle: string;
    metaDescription: string;
    metaKeywords?: string[];
  };
  category: string;
  _type: string;
  slug: {
    current: string;
  };
  publishedAt?: string;
  mainImage?: SanityImage;
}

export interface DividerConfig {
  _type: "divider";
  colorScheme: "grey" | "gold";
  includeDivider: boolean;
}

export interface ContentBlock {
  _key: string;
  _type: string;
  heading?: string;
  content?: PortableTextBlock[];
  image?: SanityImage;
  videoUrl?: string;
  description?: string;
  columns?: string[];
  rows?: { cells: string[] }[];
  faqs?: { question: string; answer: string; _key: string }[];
  isMainImage?: boolean;
  boxes?: SanityBox[];
  backgroundColor?: BackgroundColorKey;
  boxBackgroundColor: BackgroundColorKey;
  divider?: DividerConfig;

  // cta: CallToAction;
}

export interface CallToAction {
  label: string;
  url: string;
}

export interface SanityBox {
  _key: string;
  icon?: SanityImage;
  heading?: string;
  text?: string;
  link?: string;
  linkContent?: string;

  boxContentAlignment?: "string";
}

export interface SanityImage {
  _type: "image";
  asset: {
    _ref: string;
    _type: "reference";
  };
  layout?: string;
  alt?: string;
  caption?: string;
}

export type BackgroundColorKey =
  | "primary"
  | "secondary"
  | "light"
  | "dark"
  | "success"
  | "error"
  | "warning";
