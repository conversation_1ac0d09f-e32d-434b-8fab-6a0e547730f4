import { PortableTextBlock } from '@portabletext/types';

export interface SanityImage {
  _type: 'image';
  asset: {
    _ref: string;
    _type: 'reference';
  };
  alt?: string;
  caption?: string;
}

export interface ContentBlock {
  _key: string;
  _type: string;
  heading?: string;
  content?: PortableTextBlock[];
  image?: SanityImage;
  videoUrl?: string;
  description?: string;
  columns?: string[];
  rows?: { cells: string[] }[];
  faqs?: { question: string; answer: string; _key: string }[];
  isMainImage?: boolean;
  boxes?: Box[];
  boxBackgroundColor?: 'string';
  backgroundColor?: 'string';
}

export interface ContentTemplateContent {
  title: string;
  contentBlocks: ContentBlock[];
  meta: {
    metaTitle: string;
    metaDescription: string;
    metaKeywords?: string[];
  };
  category: string;
  _type: string;
  slug: {
    current: string;
  };
  publishedAt?: string;
  mainImage?: SanityImage;
}

export interface Box {
  _key: string;
  icon?: SanityImage;
  heading?: string;
  text?: string;
  link?: string;
  linkContent?: string;

  boxContentAlignment?: 'string';
}
