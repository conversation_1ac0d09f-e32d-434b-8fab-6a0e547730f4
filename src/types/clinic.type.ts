import { Address } from 'cluster';
import { Consultant } from './consultant.type';

export interface Clinic {
  id: string;
  name: string;
  description: string | null;
  telephoneNumber: string | null;
  email: string | null;
  openingHours: object | null;
  logo: string | null;
  linkToWebsite: string | null;
  diagnosticCentreFlag: boolean;
  pmsApplication: string | null;
  subscriptionType: string | null;
  pmsAPIKey: string | null;
  consultationRate: number;
  procedureRate: number;
  status: string | null;
  rating: number;
  nextInvoiceDate: string | null;
  invoiceFrequency: string | null;
  stripeCollectionMethod: string | null;
  consultants?: Consultant[];
  users?: UserActivation[];
  addressId: string | null;
  address?: Address;
  distance?: number; // Added distance property
}
