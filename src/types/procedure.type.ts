export interface Procedure {
  id: string;
  name: string;
  description: string | null;
  tierLevel: number;
  // procedureCodeAndType: CodeAndType | null;
  price: number;
  procedureCommissionRate: number | null;
  subProcedures?: Procedure[];
  // consultantSpecialityProcedures?: ConsultantSpecialityProcedure[];
  // leads?: Lead[]
  // bookings?: Booking[];
  // conditions?: Condition[]
  procedureId: string | null;
  procedure?: Procedure;
  specialityId: string | null;
  // speciality?: Speciality;
}
