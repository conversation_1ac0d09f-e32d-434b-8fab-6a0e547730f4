import { Clinic } from './clinic.type';
import { Consultant } from './consultant.type';

export interface Address {
  id: string;
  addressLine1: string;
  addressLine2: string | null;
  addressLine3: string | null;
  townCity: string | null;
  county: string | null;
  country: string | null;
  postcode: string;
  latitude: number;
  longitude: number;
  clinics?: Clinic[];
  consultants?: Consultant[];
}
