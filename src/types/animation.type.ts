import { BoxProps, SxProps } from '@mui/material';
import { ReactNode } from 'react';

export type AnimationVariant =
  | 'fade-in'
  | 'slide-up'
  | 'slide-left'
  | 'zoom-in'
  | 'float-up'
  | 'float-rise'
  | 'pop-in'
  | 'pop-spring'
  | 'reveal-in'
  | 'reveal-blur-static'
  | 'reveal-blur-dynamic';

export interface FadeInAnimationProps extends BoxProps {
  children: ReactNode;
  delay?: number;
  variant?: AnimationVariant;
  sx?: SxProps;
}
