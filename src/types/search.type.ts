import { SearchTerm } from '../lib/api/types/search-term.type';

export interface TextSectionProps {
  aiSelected: boolean;
  setAiSelected: (value: boolean) => void;
}

export interface SearchProps {
  searchTerm: SearchTerm;
  postcode: string;
  distance: number;
  inputValue: string;
  onSearchTermChange: (value: string) => void;
  onPostcodeChange: (value: string) => void;
  onDistanceChange: (value: number) => void;
  onSearch: () => void;
  setInputValue: (value: string) => void;
}

export interface SearchBarMobileProps extends SearchProps {
  open: boolean;
  setOpen: (open: boolean) => void;
}

export interface SearchResult {
  id: number;
  title: string;
  provider: string;
  description: string;
  price: string;
  oldPrice?: string;
  specialist?: string;
  specialism?: string;
  distance?: number;
  location?: {
    postcode?: string;
  };
}
