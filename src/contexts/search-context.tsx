import { useSearchReducer } from '../hooks/use-search-reducer';
import { SearchTerm } from '../lib/api/types/search-term.type';
import { createContext } from 'react';

export const SearchContext = createContext<
  | {
      searchTerm: SearchTerm | null;
      distance: number | undefined;
      postcode: string | undefined;
      updateSearchTerm: (searchTerm: SearchTerm | null) => void;
      updatePostcode: (postcode: string | undefined) => void;
      updateDistance: (distance: number | undefined) => void;
    }
  | undefined
>(undefined);

export function SearchProvider({ children }: { children: React.ReactNode }) {
  const { state, updateSearchTerm, updatePostcode, updateDistance } = useSearchReducer();

  return (
    <SearchContext.Provider value={{ ...state, updateSearchTerm, updatePostcode, updateDistance }}>
      {children}
    </SearchContext.Provider>
  );
}
