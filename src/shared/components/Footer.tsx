'use client';

import Link from 'next/link';
import Image from 'next/image';
import {
  // FormControl,
  // InputLabel,
  // InputAdornment,
  // Input,
  ListItemText,
  ListItemIcon,
  MenuItem,
  MenuList,
  Stack,
  Divider,
  Box,
  Typography,
  useTheme,
  Container,
  Grid,
} from '@mui/material';
import { IsMobile, IsTablet } from '../../lib/utils';

import SPHlogo from '../../../public/images/logos/SPH logo-new.png';
import DodeclogoWhite from '../../../public/images/logos/logoWhite.png';
import HomeOutlinedIcon from '@mui/icons-material/HomeOutlined';
import SearchOutlinedIcon from '@mui/icons-material/SearchOutlined';
// import ArticleOutlinedIcon from '@mui/icons-material/ArticleOutlined';
// import MonitorHeartOutlinedIcon from '@mui/icons-material/MonitorHeartOutlined';
// import AssignmentIndOutlinedIcon from '@mui/icons-material/AssignmentIndOutlined';
import MailIcon from '@mui/icons-material/Mail';
import logoFlowerWhite from '../../../public/logoFlowerWhite.svg';
// import { red } from '@mui/material/colors';

export default function Footer() {
  const theme = useTheme();
  const iconStyles = {
    color: 'white',
    padding: '5px',
    borderRadius: '50%',
    marginRight: '10px',
  };
  // const bulletPoints = {
  //   content: '"·"',
  //   position: 'absolute',
  //   left: '5px',
  //   bottom: 'calc(50% - 4px)',
  //   transform: 'translateX(-50%)',
  //   width: '4px',
  //   height: '4px',
  //   borderRadius: '50%',
  //   bgcolor: '#62ad3a',
  // };

  return (
    <Box
      component="footer"
      sx={{
        background: '#0C4A5F',
        backdropFilter: 'blur(8px)',
        boxShadow: '0 -2px 12px rgba(0, 0, 0, 0.1)',
        mt: 'auto',
      }}
    >
      <Box
        sx={{
          p: IsMobile() ? '24px 24px 16px' : '24px 24px 36px',
          mt: 'auto',
        }}
      >
        <Container
          maxWidth="lg"
          sx={{
            padding: IsMobile() ? '10px 0 14px 0!important' : '24px 24px 40px',
          }}
        >
          <Grid
            container
            spacing={3}
            alignItems={IsMobile() ? 'end' : 'center'}
          >
            {/* Left section  */}
            <Grid size={{ xs: 12, sm: 6 }}>
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: IsMobile() ? 'center' : 'flex-start',
                }}
              >
                <Link href={'/'}>
                  <Image
                    src={SPHlogo}
                    alt="self pay health logo"
                    width={IsMobile() ? 160 : 180}
                    style={{
                      marginBottom: '20px',
                    }}
                  />
                </Link>
                <Typography
                  variant="body1"
                  sx={{
                    color: theme.palette.common.white,
                  }}
                >
                  {/* <b> */}
                  We offer access to a number of consultants, at many locations
                  across London and the UK.
                  {/* </b> */}
                  <br />
                  <br /> In a number of specialities which patients need access
                  to the most and at a fixed initial consultation price, which
                  in many cases is cheaper than contacting the consultant
                  directly.
                </Typography>
              </Box>
            </Grid>

            {/* Middle section  */}
            <Grid size={{ xs: 6, sm: 3 }}>
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 2,
                  alignItems: 'center',
                  justifyContent: 'center',
                  // height: IsMobile() ? '100%' : 'auto',
                }}
              >
                <MenuList>
                  <Link href="/" style={{ textDecoration: 'none' }}>
                    <MenuItem
                      style={{ paddingLeft: IsMobile() ? '0' : '16px' }}
                    >
                      <ListItemIcon>
                        <HomeOutlinedIcon
                          style={{ ...iconStyles, background: '#62AD3A' }}
                        />
                      </ListItemIcon>
                      <ListItemText
                        sx={{
                          color: theme.palette.common.white,
                          '&:hover': {
                            textDecoration: 'underline',
                          },
                        }}
                        disableTypography={true}
                      >
                        <Typography variant="body2">Home</Typography>
                      </ListItemText>
                    </MenuItem>
                  </Link>

                  <Link href="/search" style={{ textDecoration: 'none' }}>
                    <MenuItem
                      style={{ paddingLeft: IsMobile() ? '0' : '16px' }}
                    >
                      <ListItemIcon>
                        <SearchOutlinedIcon
                          style={{ ...iconStyles, background: '#12925E' }}
                        />
                      </ListItemIcon>
                      <ListItemText
                        sx={{
                          color: theme.palette.common.white,
                          '&:hover': {
                            textDecoration: 'underline',
                          },
                        }}
                        disableTypography={true}
                      >
                        <Typography variant="body2">Search</Typography>
                      </ListItemText>
                    </MenuItem>
                  </Link>

                  {/* <Link href="/article" style={{ textDecoration: 'none' }}>
                    <MenuItem
                      style={{ paddingLeft: IsMobile() ? '0' : '16px' }}
                    >
                      <ListItemIcon>
                        <ArticleOutlinedIcon
                          style={{ ...iconStyles, background: '#097A6E' }}
                        />
                      </ListItemIcon>
                      <ListItemText
                        sx={{
                          color: theme.palette.common.white,
                          '&:hover': {
                            textDecoration: 'underline',
                          },
                        }}
                        disableTypography={true}
                      >
                        <Typography variant="body2">Articles</Typography>
                      </ListItemText>
                    </MenuItem>
                  </Link> */}

                  {/* <Link href="/procedures" style={{ textDecoration: 'none' }}>
                    <MenuItem
                      style={{ paddingLeft: IsMobile() ? '0' : '16px' }}
                    >
                      <ListItemIcon>
                        <MonitorHeartOutlinedIcon
                          style={{ ...iconStyles, background: '#2b768b' }}
                        />
                      </ListItemIcon>
                      <ListItemText
                        sx={{
                          color: theme.palette.common.white,
                          '&:hover': {
                            textDecoration: 'underline',
                          },
                        }}
                        disableTypography={true}
                      >
                        <Typography variant="body2">Procedures</Typography>
                      </ListItemText>
                    </MenuItem>
                  </Link> */}

                  {/* <Link href="/consultants" style={{ textDecoration: 'none' }}>
                    <MenuItem
                      style={{ paddingLeft: IsMobile() ? '0' : '16px' }}
                    >
                      <ListItemIcon>
                        <AssignmentIndOutlinedIcon
                          style={{ ...iconStyles, background: '#062933' }}
                        />
                      </ListItemIcon>
                      <ListItemText
                        sx={{
                          color: theme.palette.common.white,
                          '&:hover': {
                            textDecoration: 'underline',
                          },
                        }}
                        disableTypography={true}
                      >
                        <Typography variant="body2">Consultants</Typography>
                      </ListItemText>
                    </MenuItem>
                  </Link> */}
                </MenuList>
              </Box>
            </Grid>

            {/* Right section  */}
            <Grid size={{ xs: 6, sm: 3 }}>
              <Box
                sx={{
                  height: IsMobile() ? '100%' : 'auto',
                  paddingBottom: IsMobile() ? '18px' : 0,
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    margin: 0,
                    alignItems: IsMobile() ? 'center' : 'center',
                    textAlign: IsMobile() ? 'center' : 'left',
                    backgroundColor: '#12925E',
                    borderRadius: '14px',
                    height: IsMobile() ? '100%' : 'auto',
                  }}
                >
                  <Box
                    sx={{
                      mb: 0,
                      mt: 0,
                      width: '100%',
                      background: '#097a6e',
                      borderRadius: '14px 14px 0 0',
                      p: IsMobile() ? '1em' : '1.3em 2em',
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'flex-start',
                      flexDirection: IsTablet() ? 'column' : 'row',
                    }}
                  >
                    <Typography
                      sx={{
                        opacity: 0.9,
                        m: IsTablet() ? '0.2em 0 0.5em 0' : '0.2em 1.1em 0 0',
                        color: theme.palette.common.white,
                        fontSize: '0.6em',
                      }}
                    >
                      Powered by
                    </Typography>

                    <Link
                      href="https://dodec.co.uk/"
                      style={{ textDecoration: 'none', alignSelf: 'center' }}
                    >
                      <Image
                        src={DodeclogoWhite}
                        alt="Dodec logo"
                        style={{
                          width: IsTablet() ? '70px' : '100px',
                          height: 'auto',
                        }}
                      />
                    </Link>
                  </Box>
                  <Link
                    href="mailto:<EMAIL>"
                    style={{
                      padding: '1em 0',
                    }}
                  >
                    <Typography
                      className="onlyDesktop"
                      variant="body1"
                      sx={{
                        color: theme.palette.common.white,
                        opacity: 0.8,
                        textAlign: 'center',
                        margin: '0.2em 0 0.8em',
                      }}
                    >
                      <EMAIL>
                    </Typography>
                    <Box
                      sx={{
                        display: 'flex',
                        justifyContent: 'center',
                      }}
                    >
                      <MailIcon
                        className="onlyMobile"
                        style={{
                          color: 'white',
                          scale: 1.4,
                          alignSelf: 'center',
                        }}
                      />
                    </Box>
                  </Link>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </Container>

        {/* <Divider 
            variant="inset" 
            sx={{
              margin: 0,
              "&::before, &::after": {
                borderColor: "rgba(255,255,255,0.3)",
              },
              "&::before": {
                marginLeft: "30%",
              },
              "&::after": {
                marginRight: "30%",
              },
            }}><Image id="logoFlower" src={logoFlowerWhite} alt="self pay health logo" width={15} />
        </Divider> */}

        {/* <Box 
          sx={{
            display: 'flex',
            justifyContent: 'center',            
          }}
        >
         <Typography
              variant='overline'
              sx={{
                mb: 1,
                mt: 1.3,
                color: theme.palette.common.white,
                textTransform: 'uppercase',
                fontWeight: '100',
                textAlign: 'center',
                position: 'relative', 
                display: 'inline-block',
              }}
            >
              Explore our most interesting medical articles
              <Box
                sx={{
                  content: '""',
                  position: 'absolute',
                  bottom: 0,
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: '80px',
                  height: '1px',
                  bgcolor: '#62ad3a',
                }}
              />
         </Typography>
          
        </Box> */}

        {/* <Container maxWidth='lg' sx= {{ padding: IsMobile() ? '0 0 14px 0!important' : '24px 24px 40px'}}>
            <Grid container spacing={3} alignItems="end" >
              <Grid size={{ xs: 12, md: 6 }} sx={{order: IsMobile() ? '1' : '0'}}>
                <Box sx={{
                  width: IsMobile() ? '100%' : 'calc(310px + (534 - 310) * ((100vw - 900px) / (1260 - 900)))',
                  maxWidth: '534px',
                  display: 'flex',
                  justifyContent: 'flex-end',
                  flexDirection: 'column',
                  
                  
                }}>
                  <Typography
                    variant='caption'
                    sx={{
                      mt: 1.3,
                      color: theme.palette.common.white,
                      fontWeight: '100',
                      textAlign: IsMobile() ? 'center' : 'left',
                      position: 'relative', 
                      display: 'inline-block',
                      background: '#2b768b99',
                      borderRadius: '14px 14px 0 0',
                      padding: IsMobile() ? '1em 2em' : '1em 3em',
                      marginBottom: 0,
                      textTransform: 'uppercase'
                      
                    }}
                  >
                    Search through articles
                    
                  </Typography>
                  <Box  sx={{ 
                      background: '#2f839b33',
                      borderRadius: '0 0 14px 14px',
                      p: IsMobile() ? '0 1em 1em' : '1em 2em'
                    }} >
                    <FormControl fullWidth={true}>
                          <InputLabel  sx={{
                            color: 'white',
                          }}>
                            <Typography variant='caption'>Type here</Typography>
                          </InputLabel>
                          <Input color="warning"
                            sx={{
                              // backgroundColor: theme.palette.primary.main,
                              // borderBottom: '1px solid #0ac5b2',
                              '&::before': {
                                borderBottom: '1px solid #0ac5b2',
                              }
                            }} 
                            endAdornment={
                              <InputAdornment position="end">
                                <SearchOutlinedIcon sx={{ color: 'white' }}/>
                              </InputAdornment>
                          }/>
                    </FormControl>
                  </Box>
                  

                </Box>              
              </Grid>
              <Grid size={{ xs: 12, md: 6 }}>               
                <Box           
                  sx={{ mt: '20px',  }}>
                  <MenuList dense 
                    sx = {{
                      paddingBottom: 0,
                      paddingTop: IsMobile() ? '0' : '8px'

                    }}>
                    <Link href="" style={{ textDecoration: 'none' }}>
                      <MenuItem>           
                        <ListItemText 
                        disableTypography = {true}
                          sx={{
                          color: theme.palette.common.white,
                          '&:hover': {
                            textDecoration: 'underline',
                          },
                          }}>
                            <Typography variant= 'caption' sx={{whiteSpace: 'normal'}}>
                            Transnasal Endoscopy: A Patient-Friendly Alternative to Traditional Gastroscopy
                            </Typography>
                            <Box  sx={bulletPoints } />
                        </ListItemText>
                      </MenuItem>
                      
                    </Link>

                    <Link href="" style={{ textDecoration: 'none' }}>
                      <MenuItem>
                        
                        <ListItemText 
                          sx={{
                          color: theme.palette.common.white,
                          '&:hover': {
                            textDecoration: 'underline',
                          },
                          }}><Typography variant= 'caption' sx={{whiteSpace: 'normal'}}>Top 5 Mistakes After Knee Replacement</Typography>
                          <Box  sx={bulletPoints } />
                        </ListItemText>
                      </MenuItem>
                    </Link>
                    
                    <Link href="" style={{ textDecoration: 'none' }}>
                      <MenuItem>
                      
                        <ListItemText 
                          sx={{
                          color: theme.palette.common.white,
                          '&:hover': {
                            textDecoration: 'underline',
                          },
                          }}><Typography variant= 'caption' sx={{whiteSpace: 'normal'}}>Floaters After Cataract Surgery: Causes, Concerns, and Treatment Options</Typography>
                          <Box  sx={bulletPoints } />
                        </ListItemText>
                      </MenuItem>
                    </Link>

                    <Link href="" style={{ textDecoration: 'none' }}>
                      <MenuItem>
                      
                        <ListItemText 
                          sx={{
                          color: theme.palette.common.white,
                          '&:hover': {
                            textDecoration: 'underline',
                          },
                          }}><Typography variant= 'caption' sx={{whiteSpace: 'normal'}}>Stomach Pain 3 Days After Endoscopy</Typography>
                          <Box  sx={bulletPoints } />
                        </ListItemText>
                      </MenuItem>
                    </Link>  

                    <Link href="" style={{ textDecoration: 'none' }}>
                      <MenuItem sx={{
                        color: theme.palette.common.white,
                        paddingBottom: 0,                   
                      }}>
                      
                        <ListItemText 
                          sx={{
                            color: theme.palette.common.white,
                            '&:hover': {
                              textDecoration: 'underline',
                            },
                          }}>
                            <Typography variant= 'caption' sx={{whiteSpace: 'normal'}}>Knee Replacement Ice Packs</Typography>
                          <Box  sx={bulletPoints } />
                        </ListItemText>
                      </MenuItem>
                    </Link>     

                  </MenuList>

                </Box>
              </Grid>
            </Grid>
        </Container> */}
      </Box>
      <Box
        sx={{
          background: '#062933',
        }}
      >
        <Stack
          direction="row"
          spacing={4}
          justifyContent="center"
          sx={{
            pt: '2em',
            pb: '1em',
          }}
        >
          <Link href="/terms" style={{ textDecoration: 'none' }}>
            <Typography
              variant="body1"
              sx={{
                color: theme.palette.common.white,
                '&:hover': {
                  textDecoration: 'underline',
                },
                textTransform: 'uppercase',
                fontSize: '0.7rem',
              }}
            >
              Terms & Conditions
            </Typography>
          </Link>
          <Link href="/privacy-policy" style={{ textDecoration: 'none' }}>
            <Typography
              variant="body1"
              sx={{
                color: theme.palette.common.white,
                '&:hover': {
                  textDecoration: 'underline',
                },
                textTransform: 'uppercase',
                fontSize: '0.7rem',
              }}
            >
              Privacy Policy
            </Typography>
          </Link>
        </Stack>

        <Divider
          variant="middle"
          sx={{
            '&::before, &::after': {
              borderColor: 'rgba(255,255,255,0.3)',
            },
            '&::before': {
              marginLeft: '40%',
            },
            '&::after': {
              marginRight: '40%',
            },
          }}
        >
          <Image
            id="logoFlower"
            src={logoFlowerWhite}
            alt="self pay health logo"
            width={15}
          />
        </Divider>

        <Typography
          sx={{
            mt: 2,
            pb: 4,
            color: theme.palette.common.white,
            textAlign: 'center',
            fontSize: '0.7rem',
          }}
        >
          {' '}
          © 2024 D Medical Ltd Registered in England,
          <br className="onlyMobile" /> No. 09364536 VAT No. 216323146b.
        </Typography>
      </Box>
    </Box>
  );
}
