'use client';

import Link from 'next/link';
import Image from 'next/image';
import { Box, Typography, useTheme, Container, Grid } from '@mui/material';
import { IsMobile } from '../../lib/utils';

import logo from '../../../public/images/logos/logo-white.png';
import dodecLogo from '../../../public/images/logos/black--all_dodec_logos.png';

export default function Footer() {
  const theme = useTheme();

  return (
    <Box
      component="footer"
      sx={{
        background: 'rgba(0, 108, 102, 0.95)',
        backdropFilter: 'blur(8px)',
        boxShadow: '0 -2px 12px rgba(0, 0, 0, 0.1)',
        py: 3,
        mt: 'auto',
      }}
    >
      <Container maxWidth="xl">
        <Grid container spacing={3} alignItems="center">
          {/* Left section - Logo */}
          <Grid size={{ xs: 12, md: 2 }}>
            <Box
              sx={{
                display: 'flex',
                justifyContent: IsMobile() ? 'center' : 'flex-start',
              }}
            >
              <Image src={logo} alt="self pay health logo" width={300} />
            </Box>
          </Grid>

          {/* Middle section - Links */}
          <Grid size={{ xs: 12, md: 8 }}>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                gap: 2,
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Link href="/terms" style={{ textDecoration: 'none' }}>
                <Typography
                  variant="body1"
                  sx={{
                    color: theme.palette.common.white,
                    '&:hover': {
                      textDecoration: 'underline',
                    },
                  }}
                >
                  Terms & Conditions
                </Typography>
              </Link>
              <Link href="/privacy-policy" style={{ textDecoration: 'none' }}>
                <Typography
                  variant="body1"
                  sx={{
                    color: theme.palette.common.white,
                    '&:hover': {
                      textDecoration: 'underline',
                    },
                  }}
                >
                  Privacy Policy
                </Typography>
              </Link>
            </Box>
          </Grid>

          {/* Right section - Powered by and copyright */}
          <Grid size={{ xs: 12, md: 2 }}>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: IsMobile() ? 'center' : 'flex-start',
                textAlign: IsMobile() ? 'center' : 'left',
              }}
            >
              <Typography
                variant="body1"
                sx={{
                  color: theme.palette.common.white,
                  opacity: 0.9,
                  mb: 2,
                }}
              >
                Powered By
              </Typography>

              <Box sx={{ mb: 1 }}>
                <Image
                  src={dodecLogo}
                  alt="Dodec logo"
                  width={160}
                  height={70}
                />
              </Box>

              <Typography
                variant="body2"
                sx={{
                  color: theme.palette.common.white,
                  opacity: 0.8,
                  maxWidth: 250,
                  mb: 1,
                }}
              >
                © 2024 D Medical Ltd Registered in England, No. 09364536 <br />{' '}
                VAT No. 216323146b.
              </Typography>

              <Typography
                variant="body2"
                sx={{
                  color: theme.palette.common.white,
                  opacity: 0.8,
                }}
              >
                <EMAIL>
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
}
