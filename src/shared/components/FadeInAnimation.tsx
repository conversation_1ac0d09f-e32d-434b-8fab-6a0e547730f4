'use client';

import { useEffect, useRef, useState } from 'react';
import { Box } from '@mui/material';

import type { FadeInAnimationProps } from '@/types/animation.type';

const FadeInAnimation = ({ children, delay = 0, variant = 'fade-in', ...rest }: FadeInAnimationProps) => {
  const ref = useRef(null);
  const [isVisible, setVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setVisible(true);
          observer.disconnect();
        }
      },
      { threshold: 0.2 }
    );

    if (ref.current) observer.observe(ref.current);
    return () => observer.disconnect();
  }, []);

  const animationStyles = {
    'fade-in': {
      opacity: isVisible ? 1 : 0,
      transform: isVisible ? 'none' : 'translateY(10px)'
    },

    'slide-up': {
      opacity: isVisible ? 1 : 0,
      transform: isVisible ? 'translateY(0)' : 'translateY(40px)'
    },

    'slide-left': {
      opacity: isVisible ? 1 : 0,
      transform: isVisible ? 'translateX(0)' : 'translateX(40px)'
    },

    'zoom-in': {
      opacity: isVisible ? 1 : 0,
      transform: isVisible ? 'scale(1)' : 'scale(0.95)'
    },

    'float-up': {
      opacity: isVisible ? 1 : 0,
      transform: isVisible ? 'translateY(0) scale(1)' : 'translateY(20px) scale(0.98)'
    },

    'float-rise': {
      opacity: isVisible ? 1 : 0,
      transform: isVisible ? 'translateY(0) scale(1)' : 'translateY(40px) scale(0.96)',
      transition: isVisible ? 'all 0.7s cubic-bezier(0.23, 1, 0.32, 1)' : 'none'
    },

    'pop-in': {
      opacity: isVisible ? 1 : 0,
      transform: isVisible ? 'scale(1)' : 'scale(0.92)',
      transformOrigin: 'center'
    },

    'pop-spring': {
      opacity: isVisible ? 1 : 0,
      transform: isVisible ? 'scale(1)' : 'scale(0.8)',
      transition: isVisible ? 'all 0.6s cubic-bezier(0.22, 1, 0.36, 1)' : 'none'
    },

    'reveal-in': {
      opacity: isVisible ? 1 : 0,
      transform: isVisible ? 'translateY(0)' : 'translateY(60px)',
      filter: isVisible ? 'blur(0px)' : 'blur(6px)'
    },

    'reveal-blur-static': {
      opacity: isVisible ? 1 : 0,
      filter: isVisible ? 'blur(0px)' : 'blur(12px)',
      transition: isVisible ? 'all 0.7s cubic-bezier(0.33, 1, 0.68, 1)' : 'none'
    },
    'reveal-blur-dynamic': {
      opacity: isVisible ? 1 : 0,
      transform: isVisible ? 'translateY(0)' : 'translateY(60px)',
      filter: isVisible ? 'blur(0px)' : 'blur(12px)',
      transition: isVisible ? 'all 0.7s cubic-bezier(0.33, 1, 0.68, 1)' : 'none'
    }
  };

  return (
    <Box
      ref={ref}
      sx={{
        ...animationStyles[variant],
        transition: `all 0.6s ease-out ${delay}s`,
        willChange: 'opacity, transform'
      }}
      {...rest}
    >
      {children}
    </Box>
  );
};

export default FadeInAnimation;
