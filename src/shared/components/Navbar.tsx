'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Box,
  Typography,
  useTheme,
  IconButton,
  Drawer,
  List,
  ListItem,
  ListItemText,
} from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import { useState } from 'react';
import Image from 'next/image';

import logo from '../../../public/images/logos/SPH logo-new.png';
import { IsMobile } from '../../lib/utils';

const navItems = [
  // { label: 'Home', href: '/' },
  { label: 'Search', href: '/search' },
  // { label: 'Content', href: '/content' },
];

const NavLink = ({
  label,
  href,
  isActive,
}: {
  label: string;
  href: string;
  isActive: boolean;
}) => {
  const theme = useTheme();

  return (
    <Link href={href} style={{ textDecoration: 'none' }}>
      <Typography
        variant="body1"
        sx={{
          position: 'relative',
          color: theme.palette.common.white,
          fontWeight: isActive ? 600 : 400,
          px: 1,
          '&::after': {
            content: '""',
            position: 'absolute',
            bottom: -2,
            left: 0,
            width: isActive ? '100%' : '0%',
            height: '2px',
            backgroundColor: theme.palette.secondary.main,
            // backgroundColor: '#0C4A5F',
            transition: 'width 0.3s ease',
          },
          '&:hover::after': {
            width: '100%',
          },
        }}
      >
        {label}
      </Typography>
    </Link>
  );
};

export default function Navbar() {
  const pathname = usePathname();
  const theme = useTheme();
  const [drawerOpen, setDrawerOpen] = useState(false);

  return (
    <>
      <Box
        sx={{
          position: 'sticky',
          top: 0,
          zIndex: 1000,
          backdropFilter: 'blur(8px)',
          // background: 'rgba(0, 108, 102, 0.85)',
          background: '#0C4A5F',
          boxShadow: '0 2px 12px rgba(0, 0, 0, 0.1)',
          px: { xs: 3, md: 6 },
          py: 2,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <Link href={'/'}>
          <Image src={logo} alt="self pay health logo" width={200} />
        </Link>
        {/* <Typography
          variant="h6"
          fontWeight={600}
          sx={{ color: 'common.white', letterSpacing: '0.5px' }}
        >
          Self pay health
        </Typography> */}

        {IsMobile() ? (
          <IconButton
            onClick={() => setDrawerOpen(true)}
            sx={{ color: theme.palette.common.white }}
          >
            <MenuIcon />
          </IconButton>
        ) : (
          <Box sx={{ display: 'flex', gap: 4 }}>
            {navItems.map((item) => (
              <NavLink
                key={item.href}
                {...item}
                isActive={pathname === item.href}
              />
            ))}
          </Box>
        )}
      </Box>

      <Drawer
        anchor="right"
        open={drawerOpen}
        onClose={() => setDrawerOpen(false)}
      >
        <Box
          sx={{
            width: 240,
            py: 3,
            px: 2,
            backgroundColor: '#0C4A5F',
            height: '100%',
          }}
        >
          <Typography
            variant="h6"
            fontWeight={600}
            sx={{ color: theme.palette.common.white, mb: 2 }}
          >
            Menu
          </Typography>

          <List disablePadding>
            {navItems.map((item) => (
              <ListItem
                key={item.href}
                onClick={() => setDrawerOpen(false)}
                disableGutters
              >
                <Link
                  href={item.href}
                  style={{ textDecoration: 'none', width: '100%' }}
                >
                  <ListItemText
                    primary={item.label}
                    slotProps={{
                      primary: {
                        sx: {
                          color: theme.palette.common.white,
                          fontWeight: pathname === item.href ? 600 : 400,
                        },
                      },
                    }}
                  />
                </Link>
              </ListItem>
            ))}
          </List>
        </Box>
      </Drawer>
    </>
  );
}
