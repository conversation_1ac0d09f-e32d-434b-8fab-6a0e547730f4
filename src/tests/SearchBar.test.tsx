// import { render, screen } from '@testing-library/react';
// import SearchBar from '../components/search/SearchBar';

// describe('SearchBarDesktop', () => {
//   it('renders the search input', () => {
//     render(
//       <SearchBar
//         searchTerm=""
//         postcode=""
//         distance={25}
//         onSearchTermChange={() => {}}
//         onPostcodeChange={() => {}}
//         onDistanceChange={() => {}}
//         onSearch={() => {}}
//       />
//     );

//     expect(
//       screen.getByPlaceholderText(/search procedures or specialists/i)
//     ).toBeInTheDocument();
//   });
// });
