// import { render, screen } from '@testing-library/react';
// import ResultsSection from '../components/search/ResultsSection';
// import { describe, it, expect } from 'vitest';
// import { SearchResult } from '@/types/search';

// const mockResults: SearchResult[] = [
//   {
//     id: '1',
//     title: 'Knee Replacement',
//     provider: 'NHS',
//     description: '',
//     price: '£5000',
//   },
//   {
//     id: '2',
//     title: 'Hip Surgery',
//     provider: 'NHS',
//     description: '',
//     price: '£6000',
//   },
// ];

// vi.mock('../components/ResultsCard', () => ({
//   default: ({ result }: { result: SearchResult }) => (
//     <div data-testid="result-card">{result.title}</div>
//   ),
// }));

// describe('ResultsSection', () => {
//   it('shows loading spinner when loading is true', () => {
//     render(
//       <ResultsSection results={[]} loading={true} searchTerm="" postcode="" />
//     );
//     expect(screen.getByRole('progressbar')).toBeInTheDocument();
//   });

//   it("shows 'No results found' when there are no results", () => {
//     render(
//       <ResultsSection
//         results={[]}
//         loading={false}
//         searchTerm="heart"
//         postcode=""
//       />
//     );
//     expect(screen.getByText('No results found')).toBeInTheDocument();
//   });

//   it('renders result cards when results are present', () => {
//     render(
//       <ResultsSection
//         results={mockResults}
//         loading={false}
//         searchTerm="knee"
//         postcode="London"
//       />
//     );
//     expect(screen.getAllByTestId('result-card')).toHaveLength(2);
//     expect(screen.getByText('Knee Replacement')).toBeInTheDocument();
//   });

//   it('displays search term and postcode in description', () => {
//     render(
//       <ResultsSection
//         results={mockResults}
//         loading={false}
//         searchTerm="hip"
//         postcode="Leeds"
//       />
//     );
//     expect(screen.getByText(/“hip”/i)).toBeInTheDocument();
//     expect(screen.getByText(/near Leeds/i)).toBeInTheDocument();
//   });
// });
