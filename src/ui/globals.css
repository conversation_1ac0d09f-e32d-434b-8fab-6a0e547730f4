/* @tailwind base;
@tailwind components;
@tailwind utilities; */

:root {
  --background: #ffffff;
  --foreground: #171717;

  /*Mui Colors*/
  --primary-main: #006c66;
  --primary-light: #02d4c8;
  --primary-dark: #025752;

  --secondary-main: #c2a629;
  --secondary-light: #ffe673;
  --secondary-dark: #ab911f;

  --text-primary: #1f1f1f;
  --text-secondary: #5f5f5f;
  --text-disabled: #a9a9a9;

  /*Spacing*/
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-xxl: 3rem;
}

/* 
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
} */

html {
  font-family: "Poppins", sans-serif;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
}

body {
  font-family: var(--font-poppins);
  background-color: var(--background);
  color: var(--foreground);
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

a {
  color: inherit;
  text-decoration: none;
}

#logoFlower {
  filter: brightness(0) saturate(100%) invert(65%) sepia(68%) saturate(265%)
    hue-rotate(114deg) brightness(83%) contrast(89%);
}

/* @media (prefers-color-scheme: dark) {
  html {
    color-scheme: dark;
  }
} */

/* Portable Text Styling */
.prose-text h1,
.prose-text h2,
.prose-text h3,
.prose-text h4,
.prose-text h5,
.prose-text h6 {
  margin-top: 2rem;
  scroll-margin-top: 2rem;
}

.prose-text p {
  margin-bottom: 1.25rem;
  line-height: 1.7;
  letter-spacing: 0.01em;
  text-align: left;
  font-weight: 400;
  text-shadow: 0 0 1px rgba(0, 0, 0, 0.1);
}

.prose-text strong {
  color: var(--text-primary);
  font-weight: 600;
}

.prose-text ul,
.prose-text ol {
  padding-left: 1.5rem;
  margin: 1rem 0 1.5rem;
}

.prose-text li {
  position: relative;
  margin-bottom: 0.75rem;
  padding-left: 0.5rem;
  line-height: 1.7;
  letter-spacing: 0.01em;
  color: var(--text-secondary);
}

.prose-text li marker {
  color: var(--primary-main);
}

.prose-text h4 {
  position: relative;
  padding-left: 0;
  font-size: 1.3rem;
  color: var(--text-primary);
}

body.modal-open {
  overflow: hidden !important;
  position: fixed;
  width: 100%;
}

.content-section-constraint {
  /* max-width: 850px; */
  /* width: 100%; */
  margin-inline: auto;
}

/*  MOBILE AND DESKTOP VISIBILITY  */

.onlyDesktop {
  display: none;
}
.onlyMobile {
  display: block;
}

@media screen and (min-width: 768px) {
  .onlyDesktop {
    display: block;
  }
  .onlyMobile {
    display: none;
  }
}
