.box-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  padding: 1rem;
  margin-bottom: 1rem;

  border-radius: 1rem;
  border: 2px solid var(--darker-box-bg-color);
  background: linear-gradient(
    135deg,
    var(--box-bg-color) 0%,
    var(--darker-box-bg-color) 100%
  );
  color: var(--text-color);
  text-align: center;
  overflow: hidden;
  transition: all 0.2s ease;
}

.box-card--clickable {
  cursor: pointer;
}

.box-card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 8px 25px rgba(97, 97, 97, 0.15);
}

.box-card__icon {
  width: 70px;
  height: 70px;
  margin-bottom: 0.5rem;
  transition: transform 0.3s ease;
}

.box-card__icon:hover {
  transform: rotate(6deg) scale(1.05);
}

.box-card__heading {
  font-weight: 600;
  max-width: 30ch;
  letter-spacing: 1.1px;
}

.box-card__text {
  opacity: 0.85;
  max-width: 280px;
}

@media (min-width: 600px) {
  .box-card {
    padding: 1.25rem;
    border-radius: 1.125rem;
  }

  .box-card__icon {
    width: 76px;
    height: 76px;
  }

  .box-card:hover {
    transform: translateY(-14px) scale(1.02);
  }

  .box-card__heading {
    max-width: 36ch;
  }

  .box-card__text {
    max-width: 320px;
  }
}

@media (min-width: 900px) {
  .box-card {
    padding: 1.5rem;
  }

  .box-card__icon {
    width: 84px;
    height: 84px;
  }
}
