.general-section {
  position: relative;
  color: var(--section-text-color);
  background: var(--section-background);
  margin-top: 4rem;
  /* max-width: 850px;
  width: 90%;

  margin-inline: auto; */
}

.general-section__container {
  margin-left: 0;
  padding-left: 0;
  padding-right: 0;
}

.general-section__heading {
  position: relative;
  line-height: 1.3;
  font-weight: 600;
  color: var(--section-text-color);
  margin-bottom: 1rem;
}

.general-section__heading::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: -0.5rem;
  width: 2rem;
  height: 3px;
  border-radius: 2px;
  background-color: var(--primary-main);
  opacity: 0.6;
}

.general-section__content {
  color: var(--section-text-color);
}

/* ---------- sm ≥600px ---------- */
@media (min-width: 600px) {
  .general-section__heading {
    margin-bottom: var(--spacing-xl);
  }
}

/* ---------- md ≥900px ---------- */
@media (min-width: 900px) {
  .general-section__heading {
    line-height: 1.2;
    margin-bottom: var(--spacing-xl);
  }

  .general-section__content h1,
  .general-section__content h2,
  .general-section__content h3,
  .general-section__content h4,
  .general-section__content h5,
  .general-section__content h6 {
    margin-top: 1rem;
  }

  .general-section__content p {
    margin-bottom: 1.5rem;
    line-height: 1.8;
    text-align: justify;
  }

  .general-section__content ul,
  .general-section__content ol {
    padding-left: 1.75rem;
    margin: 1.25rem 0 1.75rem;
  }

  .general-section__content .li {
    margin-bottom: 0.875rem;
    padding-left: 0.75rem;
    line-height: 1.8;
  }

  .general-section__content h4 {
    padding-left: 1rem;
  }

  .general-section__content h4::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 0;
    display: block;
    width: 4px;
    height: 1.2em;
    transform: translateY(-50%);
    border-radius: 2px;
    background-color: var(--primary-main);
    opacity: 0.4;
  }
}
