.general-right {
  background: var(--section-background);
  margin-top: 0;
  margin-top: 4rem;
}

.general-right__heading {
  font-weight: 600;
  margin-bottom: 2rem;
  position: relative;
  line-height: 1.25;
  margin-bottom: 1rem;
}
.general-right__heading::after {
  content: "";
  position: absolute;
  bottom: -0.5rem;
  left: 0;
  width: 2rem;
  height: 3px;
  background-color: var(--primary-main);
  border-radius: 2px;
  opacity: 0.6;
}

.general-right__content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}

.general-right__image-wrap {
  position: relative;
  height: 250px;
  min-height: 0;
  border-radius: 8px;
  overflow: hidden;
}

/* Caption */
.general-right__caption {
  display: block;
  margin-top: 0.5rem;
  opacity: 0.85;
}

/* sm ≥600px */
@media (min-width: 600px) {
  .general-right__heading {
    margin-bottom: 2.5rem;
  }
}

/* md ≥900px */
@media (min-width: 900px) {
  .general-right__image-wrap {
    height: 100%;
    min-height: 400px;
  }
  .general-right__heading {
    margin-bottom: 3rem;
    line-height: 1.2;
  }
}
