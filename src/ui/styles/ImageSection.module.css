.image-section {
  background: var(--section-background);
  margin-top: 4rem;
}

.image-section__heading {
  font-weight: 600;
  margin-bottom: 2rem;
  position: relative;
  line-height: 1.25;
  margin-bottom: 2rem;
  text-align: center;
}
.image-section__heading::after {
  content: "";
  position: absolute;
  bottom: -0.5rem;
  inset-inline: 0;
  margin-inline: auto;
  width: 2rem;
  height: 3px;
  background-color: var(--primary-main);
  border-radius: 2px;
  opacity: 0.6;
}

.image-section__image-wrap {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
}

.image-section__caption {
  display: block;
  margin-top: 0.5rem;
  text-align: center;
  opacity: 0.85;
}
.image-section__description {
  margin-top: 0.75rem;
  opacity: 0.95;
  line-height: 1.6;
}

@media (min-width: 600px) {
  .image-section__heading {
    margin-bottom: 2.25rem;
  }
}

@media (min-width: 900px) {
  .image-section__heading {
    margin-bottom: 2.75rem;
    line-height: 1.2;
  }
}
