.boxes-section {
  background-color: var(--section-background);
  color: var(--section-text);
  padding: 1.5rem 1rem;
  margin-top: 4rem;
  margin-bottom: 1rem;
}

.boxes-section--narrow .boxes-section__grid {
  max-width: 66.67%;
  margin-left: auto;
  margin-right: auto;
}

.boxes-section__grid--centered {
  justify-content: center;
}

.boxes-section__col {
  min-width: 250px;
}

.boxes-section__link {
  text-decoration: none;
  color: inherit;
  display: block;
  height: 100%;
}

/* ---------- sm ≥600px ---------- */
@media (min-width: 600px) {
  .boxes-section {
    padding: 2rem 1.25rem;
  }
  .boxes-section__col {
    min-width: 300px;
  }
}

/* md ≥900px */
@media (min-width: 900px) {
  .boxes-section {
    padding: 2.5rem 1.5rem;
  }
}
