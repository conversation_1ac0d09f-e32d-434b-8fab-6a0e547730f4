.table-section {
  background: var(--section-background);
  margin-top: 4rem;
}

.table-section__heading {
  font-weight: 600;
  margin-bottom: 1rem;
  line-height: 1.2;
  margin-bottom: 2rem;
  position: relative;
}

.table-section__heading::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: -0.5rem;
  width: 2rem;
  height: 3px;
  border-radius: 2px;
  background-color: var(--primary-main);
  opacity: 0.6;
}

.table-section__description {
  margin-bottom: 1rem;
  opacity: 0.9;
}

.table-section__scroller {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.table-section__container {
  min-width: 560px;
  border-radius: 12px;
}

.table-section__table {
  width: 100%;
  border-collapse: collapse;
}

.table-section__head {
  background: var(--primary-main);
}

.table-section__head-cell {
  font-weight: 600;
  white-space: nowrap;
}

.table-section__head-text {
  font-weight: 600;
  color: white;
}

.table-section__row:nth-child(even) .table-section__cell {
  background: #e9e9e9;
}

.table-section__cell {
  vertical-align: middle;
}

.table-section__hint {
  display: block;
  margin-top: 0.5rem;
  opacity: 0.7;
  color: var(--primary-main);
  text-align: center;
}

@media (min-width: 600px) {
  .table-section__container {
    min-width: 640px;
  }
}

@media (min-width: 900px) {
  .table-section__container {
    min-width: 720px;
  }

  .table-section__hint {
    display: none;
  }
}
