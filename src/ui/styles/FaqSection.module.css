.faq-section {
  --fsx-spacing-y: 2.5rem;
  --fsx-gap: 1rem;
  --fsx-badge-size: 32px;
  --fsx-radius: 12px;

  padding: var(--fsx-spacing-y) 0;
  background: var(--section-background);
}

.faq-section__header {
  text-align: center;
  margin-bottom: var(--fsx-spacing-y);
}

.faq-section__heading {
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.faq-section__subheading {
  max-width: 600px;
  margin: 0 auto;
  opacity: 0.9;
}

.faq-section__list {
  max-width: 800px;
  margin: 0 auto;
}

.faq-section__item {
  border-radius: var(--fsx-radius);
  margin-bottom: 0.75rem;
  overflow: hidden;
}

.faq-section__summary {
  padding: 0.5rem 1rem;
}

.faq-section__summary-content {
  display: flex;
  align-items: center;
  width: 100%;
  gap: var(--fsx-gap);
}

.faq-section__badge {
  display: flex;
  align-items: center;
  justify-content: center;
  width: var(--fsx-badge-size);
  height: var(--fsx-badge-size);
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-main), var(--primary-dark));
  color: white;
  font-size: 14px;
  font-weight: 600;
  flex-shrink: 0;
}

.faq-section__question {
  font-weight: 600;
  color: #1e293b;
  line-height: 1.4;
}

.faq-section__details {
  padding: 0rem 1rem 1rem 1rem;
}

.faq-section__answer-wrap {
  padding-left: var(--fsx-gap);
  padding-top: 0.5rem;
}

.faq-section__answer {
  opacity: 0.9;
  line-height: 1.65;
}

.faq-section__expand {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--secondary-light-opacity-10);
  transition: all 0.3s ease;
}

.faq-section__expand-icon {
  color: var(--secondary-main);
  font-size: 20px;
}

.faq-section__item .Mui-expanded .faq-section__expand,
.faq-section__summary .Mui-expanded .faq-section__expand {
  background: var(--secondary-light-opacity-20);
  transform: rotate(180deg);
}

/* ---------- sm ≥600px ---------- */
@media (min-width: 600px) {
  .faq-section {
    --fsx-spacing-y: 3rem;
    --fsx-gap: 1.125rem;
    --fsx-badge-size: 34px;
    --fsx-radius: 12px;
  }

  .faq-section__summary {
    padding: 0.625rem 1.25rem;
  }

  .faq-section__details {
    padding: 0 1.25rem 1.25rem 1.25rem;
  }
}

/* ---------- md ≥900px ---------- */
@media (min-width: 900px) {
  .faq-section {
    --fsx-spacing-y: 3.5rem;
    --fsx-gap: 1.25rem;
    --fsx-badge-size: 36px;
    --fsx-radius: 14px;
  }

  .faq-section__header {
    margin-bottom: var(--fsx-spacing-y);
  }
}
