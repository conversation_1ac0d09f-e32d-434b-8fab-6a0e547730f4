.content-header {
  --ch-spacing: 0.5rem;
  --ch-radius: 12px;
  --ch-gap: 1rem;

  background-color: #fafafa;
  border: 1px solid #e0e0e0;
  border-radius: var(--ch-radius);
  padding: var(--ch-spacing);
  position: relative;
  margin-bottom: 1rem;
}

.content-header__inner {
  display: flex;
  justify-content: center;
  gap: var(--ch-gap);
}

.content-header__title {
  text-align: center;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.87);
  letter-spacing: 0.3px;
  margin-bottom: 0.5rem;
  line-height: 1.2;
}

.content-header__chip {
  background-color: white;
  font-size: 0.75rem;
}

/* ---------- sm ≥600px ---------- */
@media (min-width: 600px) {
  .content-header {
    --ch-spacing: 0.75rem;
    --ch-gap: 1.25rem;
  }

  .content-header__title {
    letter-spacing: 0.35px;
  }
}

/* ---------- md ≥900px ---------- */
@media (min-width: 900px) {
  .content-header {
    --ch-spacing: 1rem;
    --ch-gap: 1.5rem;
  }

  .content-header__title {
    letter-spacing: 0.4px;
  }
}
