.video-section {
  background: var(--section-background);
  margin-top: 4rem;
}

.video-section__heading {
  font-weight: 600;
  margin-bottom: 1rem;
  line-height: 1.2;
  margin-bottom: 2rem;
  position: relative;
}

.video-section__heading::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: -0.5rem;
  width: 2rem;
  height: 3px;
  border-radius: 2px;
  background-color: var(--primary-main);
  opacity: 0.6;
}

.video-section__frame-wrap {
  position: relative;
  width: 100%;
  overflow: hidden;
  border-radius: 12px;
  padding-bottom: 56.25%;
  background: #000;
}

.video-section__iframe {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  border: 0;
}

.video-section__description {
  margin-top: 0.75rem;
  opacity: 0.9;
  line-height: 1.6;
}
