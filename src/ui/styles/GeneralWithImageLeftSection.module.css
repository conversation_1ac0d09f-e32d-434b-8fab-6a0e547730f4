.general-left {
  background: var(--section-background);
  margin-top: 4rem;
}

.general-left__heading {
  font-weight: 600;
  line-height: 1.25;
  position: relative;
  margin-bottom: 1rem;
}
.general-left__heading::after {
  content: "";
  position: absolute;
  bottom: -0.5rem;
  left: 0;
  width: 2rem;
  height: 3px;
  background-color: var(--primary-main);
  border-radius: 2px;
  opacity: 0.6;
}

.general-left__image-wrap {
  position: relative;
  height: 250px;
  min-height: 0;
  border-radius: 8px;
  overflow: hidden;
}

.general-left__caption {
  display: block;
  margin-top: 0.5rem;
  opacity: 0.85;
}

.general-left__content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}

/* sm ≥600px */
@media (min-width: 600px) {
  .general-left__heading {
    margin-bottom: var(--spacing-xl);
  }
}

/* md ≥900px */
@media (min-width: 900px) {
  .general-left__image-wrap {
    height: 100%;
    min-height: 400px;
  }
  .general-left__heading {
    margin-bottom: var(--spacing-xl);
    line-height: 1.2;
  }
}
