'use client';

import { <PERSON>acheProvider } from '@emotion/react';
import {
  ThemeProvider as MUIThemeProvider,
  createTheme,
} from '@mui/material/styles';
import { ReactNode } from 'react';
import { emotionCache } from '../utils/emotion-cache';
import { CssBaseline } from '@mui/material';

const theme = createTheme({
  typography: {
    fontFamily: "'Poppins', sans-serif",
    h1: {
      fontWeight: 700,
      fontSize: 'clamp(2.25rem, 6.5vw, 5rem)',
      lineHeight: 1.05,
    },
    h2: {
      fontWeight: 600,
      fontSize: 'clamp(1.75rem, 5vw, 3.5rem)',
      lineHeight: 1.1,
    },
    h3: {
      fontWeight: 600,
      fontSize: 'clamp(1.5rem, 4vw, 2.5rem)',
      lineHeight: 1.15,
    },
    h4: {
      fontWeight: 500,
      fontSize: 'clamp(1.2rem, 3vw, 1.75rem)',
      lineHeight: 1.2,
    },
    h5: {
      fontWeight: 500,
      fontSize: 'clamp(1rem, 1.8vw, 1.2rem)',
      lineHeight: 1.25,
    },
    h6: {
      fontWeight: 400,
      fontSize: 'clamp(0.8rem, 1vw, 1.1rem)',
      lineHeight: 1.3,
    },
    body1: { fontSize: 'clamp(1.0rem, 1.7vw, 1.2rem)', lineHeight: 1.65 },
    body2: { fontSize: 'clamp(0.8rem, 1.5vw, 1.1rem)', lineHeight: 1.65 },

    caption: {
      fontSize: 'clamp(0.7rem, 1.1vw, 0.8rem)',
    },
    overline: {
      fontSize: 'clamp(0.75rem, 1.3vw, 0.9rem)',
    },
  },
  palette: {
    primary: {
      main: '#006c66',
      light: '#02d4c8',
      dark: '#025752',
    },
    secondary: {
      main: '#c2a629',
      light: '#FFE673',
      dark: '#ab911f',
    },

    text: {
      primary: '#1f1f1f',
      secondary: '#5f5f5f',
      disabled: '#a9a9a9',
    },
  },

  components: {
    MuiButton: {
      defaultProps: {
        className: 'primary-button',
      },
      styleOverrides: {
        root: ({ theme }) => ({
          background: `linear-gradient(90deg, ${theme.palette.secondary.main} 0%, ${theme.palette.secondary.light} 100%)`,
          boxShadow: 'rgba(202, 174, 47, 0.2) 0px 8px 24px',
          color: 'white',
          borderRadius: '10px',
          fontWeight: 'bold',
          padding: '1rem',
        }),
      },
    },
    MuiAccordion: {
      variants: [
        {
          props: { variant: 'outlined' },
          style: {
            marginBottom: 16,
            borderRadius: 16,
            border: '1px solid rgba(226, 232, 240, 0.5)',
            background: 'rgba(255, 255, 255, 0.9)',
            overflow: 'hidden',
            transition: 'all 0.3s',
          },
        },
      ],
      styleOverrides: {
        root: {
          '&:hover': {
            transform: 'translateY(-2px)',
            boxShadow: '0 8px 25px rgba(0,108,102,0.1)',
            border: '1px solid rgba(0,108,102,0.3)',
          },
          '&.Mui-expanded': {
            margin: '0 0 16px 0',
            boxShadow: '0 8px 25px rgba(0,108,102,0.1)',
            border: '1px solid rgba(0,108,102,0.3)',
          },
        },
      },
    },
    MuiAccordionSummary: {
      styleOverrides: {
        root: {
          padding: '16px 24px',
          '& .MuiAccordionSummary-expandIconWrapper': {
            marginLeft: 8,
            transition: 'transform .3s',
          },
          '&.Mui-expanded .MuiAccordionSummary-expandIconWrapper': {
            transform: 'rotate(180deg)',
          },
        },
      },
    },
    MuiAccordionDetails: {
      styleOverrides: {
        root: {
          padding: '0 24px 24px',
          borderTop: '1px solid rgba(226,232,240,0.3)',
        },
      },
    },
  },
});

export function ThemeProvider({ children }: { children: ReactNode }) {
  return (
    <CacheProvider value={emotionCache}>
      <MUIThemeProvider theme={theme}>
        <CssBaseline />
        {children}
      </MUIThemeProvider>
    </CacheProvider>
  );
}
