/* @tailwind base;
@tailwind components;
@tailwind utilities; */

:root {
  --background: #ffffff;
  --foreground: #171717;
}
/* 
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
} */

html {
  font-family: "Poppins", sans-serif;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
}

body {
  font-family: var(--font-poppins);
  background-color: var(--background);
  color: var(--foreground);
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

a {
  color: inherit;
  text-decoration: none;
}

/* @media (prefers-color-scheme: dark) {
  html {
    color-scheme: dark;
  }
} */

/* Portable Text Styling */
.prose {
  max-width: 100%;
}

.prose p {
  margin-bottom: 1rem;
  line-height: 1.6;
}

.prose ul, .prose ol {
  margin-left: 1.5rem;
  margin-bottom: 1rem;
}

.prose li {
  margin-bottom: 0.3rem;
}

.prose a {
  color: var(--mui-palette-primary-main);
  text-decoration: underline;
}

.prose a:hover {
  text-decoration: none;
}

.prose blockquote {
  font-style: italic;
  border-left: 4px solid var(--mui-palette-primary-main);
  padding-left: 1rem;
  margin-left: 0;
  margin-right: 0;
}
