'use client';

import { useState, useEffect } from 'react';

// Generate UUID v4
function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0;
    const v = c == 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

interface UsePersistentUserIdReturn {
  userId: string | null;
  isLoaded: boolean;
  hasPerformanceConsent: boolean;
}

declare global {
  interface Window {
    CookieFirst?: {
      consent?: {
        necessary?: boolean;
        performance?: boolean;
        functional?: boolean;
        advertising?: boolean;
      };
    };
  }
}

export function usePersistentUserId(): UsePersistentUserIdReturn {
  const [userId, setUserId] = useState<string | null>(null);
  const [isLoaded, setIsLoaded] = useState<boolean>(false);
  const [hasPerformanceConsent, setHasPerformanceConsent] =
    useState<boolean>(false);

  useEffect(() => {
    const USER_ID_KEY = 'medical_marketplace_user_id';

    const checkPerformanceConsent = () => {
      if (typeof window !== 'undefined' && window.CookieFirst?.consent) {
        return window.CookieFirst.consent.performance === true;
      }
      return false;
    };

    const initializeUserId = () => {
      const performanceConsent = checkPerformanceConsent();
      setHasPerformanceConsent(performanceConsent);

      let id: string | null = null;

      // ALWAYS check localStorage first (most persistent)
      id = localStorage.getItem(USER_ID_KEY);

      if (!id) {
        // Check sessionStorage second
        id = sessionStorage.getItem(USER_ID_KEY);
      }

      if (!id) {
        // Generate new ID only if neither storage has one
        id = `anon_${generateUUID()}`;
      }

      // Now store in appropriate location based on consent
      if (performanceConsent) {
        // Store in localStorage (persistent)
        localStorage.setItem(USER_ID_KEY, id);
        // Clean up sessionStorage if it exists
        sessionStorage.removeItem(USER_ID_KEY);
      } else {
        // Store in sessionStorage (temporary)
        sessionStorage.setItem(USER_ID_KEY, id);
      }

      // Only update state if the ID actually changed
      setUserId((prevId) => {
        if (prevId !== id) {
          return id;
        }
        return prevId;
      });

      setIsLoaded(true);
    };

    // Initial setup
    initializeUserId();

    // Listen for consent changes (but don't regenerate ID)
    const handleConsentChange = () => {
      setTimeout(() => {
        initializeUserId();
      }, 100);
    };

    window.addEventListener('cf_services_consent', handleConsentChange);

    return () => {
      window.removeEventListener('cf_services_consent', handleConsentChange);
    };
  }, []);

  return { userId, isLoaded, hasPerformanceConsent };
}
