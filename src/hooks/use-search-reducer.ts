import { useReducer } from 'react';
import { SearchReducer } from '../reducers/search-reducrer';
import { SearchActionType } from '../shared/enums/search-action-type';
import { SearchTerm } from '../lib/api/types/search-term.type';

const initialState = {
  searchTerm: null,
  postcode: undefined,
  distance: undefined
};

export const useSearchReducer = () => {
  const [state, dispatch] = useReducer(SearchReducer, initialState);

  const updateSearchTerm = (searchTerm: SearchTerm | null) =>
    dispatch({
      type: SearchActionType.SET_SEARCH_TERM,
      payload: searchTerm
    });

  const updatePostcode = (postcode: string | undefined) =>
    dispatch({
      type: SearchActionType.SET_POSTCODE,
      payload: postcode
    });

  const updateDistance = (distance: number | undefined) =>
    dispatch({
      type: SearchActionType.SET_DISTANCE,
      payload: distance
    });

  return {
    state,
    updateSearchTerm,
    updatePostcode,
    updateDistance
  };
};
