import { Clinic } from '@/types/clinic.type';
import { trackProviderClick } from '@/utils/tracking';
import { useEffect, useState } from 'react';
import { SearchBarParams } from '@/lib/api/types/searchbar-params.type';

//Model state management
export const useClinicModal = () => {
  const [selectedClinic, setSelectedClinic] = useState<Clinic | null>(null);
  const [selectedIndex, setSelectedIndex] = useState<number>(0);

  const isOpen = selectedClinic !== null;

  useEffect(() => {
    if (isOpen) {
      document.body.classList.add('modal-open');
    } else {
      document.body.classList.remove('modal-open');
    }

    return () => {
      document.body.classList.remove('modal-open');
    };
  }, [isOpen]);

  const openModal = (clinic: Clinic, index: number) => {
    setSelectedClinic(clinic);
    setSelectedIndex(index);
  };

  const closeModal = () => {
    setSelectedClinic(null);
    setSelectedIndex(0);
  };

  return {
    selectedClinic,
    selectedIndex,
    isOpen: isOpen,
    openModal,
    closeModal,
  };
};

//Model helper functions
export function getClinicInfo(clinic: Clinic) {
  const distanceInMiles = clinic.distance
    ? Math.round((clinic.distance / 1609.34) * 10) / 10
    : null;

  const displayDescription =
    clinic.description || `${clinic.name} is a clinic which`;

  return { distanceInMiles, displayDescription };
}

type LeadActionType = 'website' | 'phone';
export function useClinicLeadActions(
  clinic: Clinic,
  index: number,
  searchParams: SearchBarParams,
  searchSource: 'manual' | 'ai'
) {
  const fire = (actionType: LeadActionType) =>
    trackProviderClick({
      searchTerm: searchParams.searchTerm,
      postcode: searchParams.postcode,
      distance: searchParams.distance,
      searchSource,
      clinicId: clinic.id,
      clinicName: clinic.name,
      index: index + 1,
      actionType,
      websiteUrl: clinic.linkToWebsite || null,
      phoneNumber: clinic.telephoneNumber || null,
      rating: clinic.rating || null,
    });

  const onWebsiteClick = () => fire('website');
  const onPhoneClick = () => fire('phone');

  return { onWebsiteClick, onPhoneClick };
}
