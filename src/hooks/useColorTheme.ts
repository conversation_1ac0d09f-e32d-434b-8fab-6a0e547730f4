import { useTheme } from '@mui/material/styles';
import { getColorMap, ColorKey, ColorConfig } from '@/lib/colorMap.ts';

/**
 * Custom hook for handling color themes across components
 * Provides a unified way to get background and text colors
 */
export function useColorTheme() {
  const theme = useTheme();
  const colorMap = getColorMap(theme);

  /**
   * Get color configuration for a given color key
   * @param colorKey - The color key from Sanity
   * @param fallback - Fallback color key if the provided one is invalid
   * @returns ColorConfig object with background and text colors
   */
  const getColors = (
    colorKey: string | undefined,
    fallback: ColorKey = 'light'
  ): ColorConfig => {
    const key = (colorKey as ColorKey) || fallback;
    return colorMap[key] || colorMap[fallback];
  };

  /**
   * Get background color only
   * @param colorKey - The color key from Sanity
   * @param fallback - Fallback color key
   * @returns Background color string
   */
  const getBackgroundColor = (
    colorKey: string | undefined,
    fallback: ColorKey = 'light'
  ): string => {
    return getColors(colorKey, fallback).background;
  };

  /**
   * Get text color only
   * @param colorKey - The color key from Sanity
   * @param fallback - Fallback color key
   * @returns Text color string
   */
  const getTextColor = (
    colorKey: string | undefined,
    fallback: ColorKey = 'light'
  ): string => {
    return getColors(colorKey, fallback).text;
  };

  return {
    getColors,
    getBackgroundColor,
    getTextColor,
    colorMap,
    theme,
  };
}

/**
 * Convenience hook specifically for section background colors
 * @param backgroundColor - Background color key from Sanity
 * @returns ColorConfig object
 */
export function useSectionColors(backgroundColor?: string): ColorConfig {
  const { getColors } = useColorTheme();
  return getColors(backgroundColor, 'white');
}

/**
 * Convenience hook specifically for box background colors
 * @param boxBackgroundColor - Box background color key from Sanity
 * @returns ColorConfig object
 */
export function useBoxColors(boxBackgroundColor?: string): ColorConfig {
  const { getColors } = useColorTheme();
  return getColors(boxBackgroundColor, 'light');
}
