import { UseChatHelpers } from '@ai-sdk/react';
import { useEffect, useState } from 'react';

export function useMessages({ chatId, status }: { chatId: string; status: UseChatHelpers['status'] }) {
  const [hasSentMessage, setHasSentMessage] = useState(false);

  useEffect(() => {
    if (chatId) {
      setHasSentMessage(false);
    }
  }, [chatId]);

  useEffect(() => {
    if (status === 'submitted') {
      setHasSentMessage(true);
    }
  }, [status]);

  return {
    hasSentMessage
  };
}
