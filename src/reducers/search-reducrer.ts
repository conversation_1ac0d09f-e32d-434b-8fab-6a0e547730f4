import { Reducer } from 'react';
import { SearchProviderAction } from '../types/search-provider-action.type';
import { SearchState } from '../types/search-state.interface';
import { SearchActionType } from '../shared/enums/search-action-type';

export const SearchReducer: Reducer<SearchState, SearchProviderAction> = (state, action) => {
  const { type, payload } = action;

  switch (type) {
    case SearchActionType.SET_SEARCH_TERM:
      return {
        ...state,
        searchTerm: payload
      };
    case SearchActionType.SET_POSTCODE:
      return {
        ...state,
        postcode: payload
      };
    case SearchActionType.SET_DISTANCE:
      return {
        ...state,
        distance: payload
      };
    case SearchActionType.RESET_STATE:
    default:
      return {
        searchTerm: null,
        postcode: undefined,
        distance: undefined
      };
  }
};
