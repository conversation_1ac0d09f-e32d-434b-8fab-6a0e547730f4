import { generateUUID } from '@/lib/utils';

// Get current user ID (for use outside React components)
export const getCurrentUserId = (): string | null => {
  if (typeof window === 'undefined') return null;

  // Try localStorage first, then sessionStorage
  const USER_ID_KEY = 'medical_marketplace_user_id';
  return (
    localStorage.getItem(USER_ID_KEY) ||
    sessionStorage.getItem(USER_ID_KEY) ||
    null
  );
};

// Base event interface
interface BaseEvent {
  event: string;
  user_id?: string | null;
}

// Enhanced dataLayer push with user ID
export const trackEvent = (eventData: BaseEvent & Record<string, unknown>) => {
  const userId = getCurrentUserId();

  window.dataLayer = window.dataLayer || [];
  window.dataLayer.push({
    ...eventData,
    user_id: userId,
  });

  console.log('Event tracked:', eventData.event, eventData);
};

// Specific tracking interfaces
interface SearchTrackingParams {
  searchTerm: {
    name: string;
    id: string;
    type: string;
  };
  searchSource: 'manual' | 'ai';
  resultsCount: number;
  postcode?: string;
  distance?: number;
}

interface ViewItemListParams {
  searchTerm: {
    name: string;
    id: string;
    type: string;
  };
  searchSource: 'manual' | 'ai';
  items: Array<{
    item_id: string;
    item_name: string;
    index: number;
    rating: number | null;
  }>;
}

interface ViewItemParams {
  clinicId: string;
  clinicName: string;
  index: number;
  rating: number | null;
}

interface ProviderClickParams {
  clinicId: string;
  clinicName: string;
  index: number;
  actionType: 'website' | 'phone';
  websiteUrl?: string | null;
  phoneNumber?: string | null;
  rating: number | null;
}

// Specific tracking functions
export const trackSearch = (params: SearchTrackingParams): void => {
  trackEvent({
    event: 'search_results_displayed',
    search_term: params.searchTerm.name,
    search_term_id: params.searchTerm.id,
    search_term_type: params.searchTerm.type,
    search_source: params.searchSource,
    results_count: params.resultsCount,
    search_postcode: params.postcode || null,
    search_distance: params.distance || null,
  });
};

export const trackViewItemList = (params: ViewItemListParams): void => {
  trackEvent({
    event: 'view_item_list',
    search_term: params.searchTerm.name,
    search_term_id: params.searchTerm.id,
    search_term_type: params.searchTerm.type,
    search_source: params.searchSource,
    item_list_name: 'Search Results',
    items: params.items,
  });
};

// export const trackViewItem = (params: ViewItemParams): void => {
//   trackEvent({
//     event: 'view_item',
//     items: {
//       clinic_id: params.clinicId,
//       clinic_name: params.clinicName,
//       index: params.index,
//       rating: params.rating,
//     },
//   });
// };
export const trackViewItem = (params: ViewItemParams): void => {
  trackEvent({
    event: 'view_item',
    items: [
      {
        clinic_id: params.clinicId,
        clinic_name: params.clinicName,
        index: params.index,
        rating: params.rating,
      },
    ],
  });
};
// export const trackLeadGeneration = (params: LeadGenerationParams): void => {
//   trackEvent({
//     event: 'generate_lead',
//     clinic_id: params.clinicId,
//     clinic_name: params.clinicName,
//     index: params.index,
//     action_type: params.actionType,
//     website_url: params.websiteUrl || null,
//     phone_number: params.phoneNumber || null,
//     rating: params.rating,
//   });
// };

export const trackProviderClick = (params: ProviderClickParams): void => {
  trackEvent({
    event: 'purchase',
    currency: 'GBP',
    value: 1, // Assign arbitrary value for tracking
    transaction_id: `referral_${params.clinicName}_${generateUUID().split('-')[0]}`, // Unique ID
    items: [
      {
        item_id: params.clinicId,
        item_name: params.clinicName,
        index: params.index,
        item_variant: params.actionType,
        quantity: 1,
        price: 1, // Arbitrary value
      },
    ],
    // Your custom parameters
    action_type: params.actionType,
    website_url: params.websiteUrl,
    phone_number: params.phoneNumber,
  });
};
