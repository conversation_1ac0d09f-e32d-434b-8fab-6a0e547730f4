import { generateUUID } from '@/lib/utils';

// Declare fbq for TypeScript
declare global {
  interface Window {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    fbq: any;
  }
}

// Helper function to safely call Facebook Pixel
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const fbq = (...args: any[]) => {
  if (typeof window !== 'undefined' && window.fbq) {
    window.fbq(...args);
  }
};

// Get current user ID (for use outside React components)
export const getCurrentUserId = (): string | null => {
  if (typeof window === 'undefined') return null;

  // Try localStorage first, then sessionStorage
  const USER_ID_KEY = 'medical_marketplace_user_id';
  return (
    localStorage.getItem(USER_ID_KEY) ||
    sessionStorage.getItem(USER_ID_KEY) ||
    null
  );
};

// Base event interface
interface BaseEvent {
  event: string;
  user_id?: string | null;
}

// Enhanced dataLayer push with user ID
export const trackEvent = (eventData: BaseEvent & Record<string, unknown>) => {
  const userId = getCurrentUserId();

  window.dataLayer = window.dataLayer || [];
  window.dataLayer.push({
    ...eventData,
    user_id: userId,
  });

  // console.log('Event tracked:', eventData.event, eventData);
};

// Specific tracking interfaces
interface SearchTrackingParams {
  searchTerm: {
    name: string;
    id: string;
    type: string;
  };
  searchSource: 'manual' | 'ai';
  resultsCount: number;
  postcode?: string;
  distance?: number;
}

interface ViewItemListParams {
  searchTerm: {
    name: string;
    id: string;
    type: string;
  };
  searchSource: 'manual' | 'ai';
  items: Array<{
    id: string;
    name: string;
    index: number;
    rating: number | null;
    quantity: number;
  }>;
  postcode?: string;
  distance?: number;
}

interface ViewItemParams {
  // searchTerm: string | undefined;
  searchTerm:
    | {
        name: string;
        id: string;
        type: string;
      }
    | undefined;
  clinicId: string;
  clinicName: string;
  index: number;
  rating: number | null;
  searchSource?: 'manual' | 'ai';
  postcode?: string;
  distance?: number;
}

interface ProviderClickParams {
  // searchTerm: string | undefined;
  searchTerm:
    | {
        name: string;
        id: string;
        type: string;
      }
    | undefined;
  clinicId: string;
  clinicName: string;
  index: number;
  actionType: 'website' | 'phone';
  websiteUrl?: string | null;
  phoneNumber?: string | null;
  rating: number | null;
  searchSource?: 'manual' | 'ai';
  postcode?: string;
  distance?: number;
}

// Specific tracking functions
export const trackSearch = (params: SearchTrackingParams): void => {
  trackEvent({
    event: 'search_results_displayed',
    search_term: params.searchTerm.name,
    search_term_id: params.searchTerm.id,
    search_term_type: params.searchTerm.type,
    search_source: params.searchSource,
    results_count: params.resultsCount,
    postcode: params.postcode || null,
    distance: params.distance || null,
  });
};

export const trackViewItemList = (params: ViewItemListParams): void => {
  trackEvent({
    event: 'view_item_list',
    search_term: params.searchTerm.name,
    search_term_id: params.searchTerm.id,
    search_term_type: params.searchTerm.type,
    search_source: params.searchSource,
    item_list_name: 'Search Results',
    items: params.items,
    postcode: params.postcode || null,
    distance: params.distance || null,
  });

  // Send to Facebook Pixel
  fbq('track', 'Search', {
    search_string: params.searchTerm.name,
    content_category: params.searchTerm.type,
    content_type: 'product',
    contents: params.items,
  });

  fbq('trackCustom', 'HealthcareSearch', {
    search_string: params.searchTerm.name,
    content_category: params.searchTerm.type,
    content_type: 'product',
    contents: params.items,
    postcode: params.postcode,
    distance: params.distance,
  });
};

export const trackViewItem = (params: ViewItemParams): void => {
  trackEvent({
    event: 'view_item',
    search_term: params.searchTerm?.name,
    search_term_id: params.searchTerm?.id,
    search_term_type: params.searchTerm?.type,
    searchSource: params.searchSource,
    postcode: params.postcode,
    distance: params.distance,
    items: [
      {
        clinic_id: params.clinicId,
        clinic_name: params.clinicName,
        index: params.index,
        rating: params.rating,
      },
    ],
  });

  fbq('track', 'ViewContent', {
    content_type: 'provider_profile',
    contents: [
      {
        id: params.clinicId,
        name: params.clinicName,
        index: params.index,
        rating: params.rating,
        quantity: 1,
      },
    ],
    content_name: params.clinicName,
    content_category: params.searchTerm?.type,
  });

  // Custom Facebook event with search context and detailed data
  fbq('trackCustom', 'ProviderProfileView', {
    provider_id: params.clinicId,
    provider_name: params.clinicName,
    index: params.index,
    search_term: params.searchTerm?.name,
    search_term_id: params.searchTerm?.id,
    search_term_type: params.searchTerm?.type,
    searchSource: params.searchSource,
    postcode: params.postcode,
    distance: params.distance,
  });
};

export const trackProviderClick = (params: ProviderClickParams): void => {
  trackEvent({
    event: 'purchase',
    currency: 'GBP',
    value: 1, // Assign arbitrary value for tracking
    transaction_id: `referral_${params.clinicName}_${generateUUID().split('-')[0]}`, // Unique ID
    search_term: params.searchTerm?.name,
    search_term_id: params.searchTerm?.id,
    search_term_type: params.searchTerm?.type,
    searchSource: params.searchSource,
    postcode: params.postcode,
    distance: params.distance,
    items: [
      {
        item_id: params.clinicId,
        item_name: params.clinicName,
        index: params.index,
        item_variant: params.actionType,
        quantity: 1,
        price: 1, // Arbitrary value
      },
    ],
    action_type: params.actionType,
    website_url: params.websiteUrl,
    phone_number: params.phoneNumber,
  });

  fbq('track', 'Purchase', {
    content_type: 'provider_referal',
    currency: 'GBP',
    value: 1, // Arbitrary value
    contents: [
      {
        id: params.clinicId,
        name: params.clinicName,
        index: params.index,
        rating: params.rating,
        quantity: 1,
      },
    ],
    content_name: params.clinicName,
    content_category: params.searchTerm?.type,
  });

  fbq('trackCustom', 'ProviderClick', {
    provider_id: params.clinicId,
    provider_name: params.clinicName,
    index: params.index,
    search_term: params.searchTerm?.name,
    search_term_id: params.searchTerm?.id,
    search_term_type: params.searchTerm?.type,
    search_source: params.searchSource || '',
    action_type: params.actionType,
    postcode: params.postcode,
    distance: params.distance,
  });
};

// export const trackLeadGeneration = (params: LeadGenerationParams): void => {
//   trackEvent({
//     event: 'generate_lead',
//     clinic_id: params.clinicId,
//     clinic_name: params.clinicName,
//     index: params.index,
//     action_type: params.actionType,
//     website_url: params.websiteUrl || null,
//     phone_number: params.phoneNumber || null,
//     rating: params.rating,
//   });
// };
