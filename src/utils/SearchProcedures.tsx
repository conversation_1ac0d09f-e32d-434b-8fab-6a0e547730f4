import { ProceduresSearchParams } from '../lib/api/types/procedures.type';
import { dummyResults } from './searchData';

export const searchProcedures = async ({ searchTerm, postcode }: ProceduresSearchParams) => {
  return dummyResults.filter((item) => {
    const keywordMatch =
      !searchTerm ||
      item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.provider.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.specialist?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.specialism?.toLowerCase().includes(searchTerm.toLowerCase());

    const postcodeMatch = !postcode || item.location?.postcode?.toLowerCase().includes(postcode.toLowerCase());

    return keywordMatch && postcodeMatch;
  });
};
