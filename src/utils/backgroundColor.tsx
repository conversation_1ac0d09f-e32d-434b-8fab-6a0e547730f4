import { useTheme } from '@mui/material/styles';
import { getColorMap } from '@/lib/colorMap.ts';
import { ContentBlock } from '@/types/content.types';

interface Props {
  block: ContentBlock;
}

export type BackgroundColorKey =
  | 'primary'
  | 'secondary'
  | 'light'
  | 'dark'
  | 'success'
  | 'error'
  | 'warning';

export default function GetBackgroundColor({ block }: Props) {
  const theme = useTheme();

  const backgroundColorKey: BackgroundColorKey =
    (block.backgroundColor as BackgroundColorKey) || 'light';

  const colorMap = getColorMap(theme);

  const bgColor = colorMap[backgroundColorKey]?.background || 'white';
  const textColor =
    colorMap[backgroundColorKey]?.text || theme.palette.text.primary;

  return {
    bgColor,
    textColor,
  };
}
