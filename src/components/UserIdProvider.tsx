'use client';

import { useEffect, useRef } from 'react';
import { usePersistentUserId } from '../hooks/use-persistentUserId';

export default function UserIdProvider() {
  const { userId, isLoaded } = usePersistentUserId();
  const sentToDataLayer = useRef(false);

  useEffect(() => {
    // Only send to dataLayer once per page load
    if (isLoaded && userId && !sentToDataLayer.current) {
      window.dataLayer = window.dataLayer || [];

      window.dataLayer.push({
        event: 'user_id_ready',
        user_id: userId,
      });

      sentToDataLayer.current = true;
    }
  }, [userId, isLoaded]);

  return null;
}
