import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from "@mui/material";
import { ContentBlock } from "../../types/content.types";
import { getStaticSectionColors } from "@/lib/staticColorMap";
import styles from "@/ui/styles/TableSection.module.css";

export function TableSection({ block }: { block: ContentBlock }) {
  const sectionColors = getStaticSectionColors(block.backgroundColor);
  if (!block.columns || !block.rows || block.rows.length === 0) return null;

  return (
    <Box
      className={`${styles["table-section"]} content-section-constraint`}
      style={
        {
          "--section-background": sectionColors.background,
        } as React.CSSProperties
      }
    >
      {block.heading && (
        <Typography variant="h4" className={styles["table-section__heading"]}>
          {block.heading}
        </Typography>
      )}

      {block.description && (
        <Typography
          variant="body2"
          className={styles["table-section__description"]}
        >
          {block.description}
        </Typography>
      )}

      <Box className={styles["table-section__scroller"]}>
        <TableContainer
          component={Paper}
          className={styles["table-section__container"]}
        >
          <Table
            className={styles["table-section__table"]}
            aria-label={block.heading || "Data table"}
          >
            <TableHead className={styles["table-section__head"]}>
              <TableRow>
                {block.columns.map((column, index) => (
                  <TableCell
                    key={index}
                    className={styles["table-section__head-cell"]}
                  >
                    <Typography
                      variant="h6"
                      className={styles["table-section__head-text"]}
                    >
                      {column}
                    </Typography>
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>

            <TableBody>
              {block.rows.map((row, rowIndex) => (
                <TableRow
                  key={rowIndex}
                  className={styles["table-section__row"]}
                >
                  {row.cells.map((cell, cellIndex) => (
                    <TableCell
                      key={cellIndex}
                      className={styles["table-section__cell"]}
                    >
                      <Typography variant="h6">{cell}</Typography>
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>

      <Typography variant="caption" className={styles["table-section__hint"]}>
        Swipe left or right to view more columns
      </Typography>
    </Box>
  );
}
