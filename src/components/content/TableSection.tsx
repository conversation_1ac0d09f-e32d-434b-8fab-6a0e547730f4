import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from '@mui/material';
import { ContentBlock } from '../../types/content.types';

import { getStaticSectionColors } from '@/lib/staticColorMap';

export function TableSection({ block }: { block: ContentBlock }) {
  const sectionColors = getStaticSectionColors(block.backgroundColor);

  if (!block.columns || !block.rows || block.rows.length === 0) return null;

  return (
    <Box sx={{ mb: 4, background: sectionColors.background }}>
      {block.heading && (
        <Typography variant="h4" component="h2" sx={{ mb: 2 }}>
          {block.heading}
        </Typography>
      )}
      {block.description && (
        <Typography variant="body2" sx={{ mb: 2 }}>
          {block.description}
        </Typography>
      )}
      <TableContainer component={Paper} sx={{ boxShadow: 2, borderRadius: 1 }}>
        <Table
          sx={{ minWidth: 650 }}
          aria-label={block.heading || 'Data table'}
        >
          <TableHead sx={{ bgcolor: 'primary.light' }}>
            <TableRow>
              {block.columns.map((column, index) => (
                <TableCell key={index} sx={{ fontWeight: 'bold' }}>
                  {column}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {block.rows.map((row, rowIndex) => (
              <TableRow
                key={rowIndex}
                sx={{ '&:nth-of-type(odd)': { bgcolor: 'action.hover' } }}
              >
                {row.cells.map((cell, cellIndex) => (
                  <TableCell key={cellIndex}>{cell}</TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
}
