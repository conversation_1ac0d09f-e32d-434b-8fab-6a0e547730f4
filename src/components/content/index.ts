import { GeneralSection } from './GeneralSection';
import { GeneralWithImageLeftSection } from './GeneralWithImageLeftSection';
import { GeneralWithImageRightSection } from './GeneralWithImageRightSection';
import { ImageSection } from './ImageSection';
import { VideoSection } from './VideoSection';
import { FaqSection } from './FaqSection';
import { TableSection } from './TableSection';
import { ContentBlock } from '../../types/content.types';
import React from 'react';
import BoxesSection from './BoxesSection';

// Component map for rendering different section types

export const SectionComponentMap: Record<
  string,
  React.FC<{ block: ContentBlock }>
> = {
  generalSection: GeneralSection,
  generalWithImageLeftSection: GeneralWithImageLeftSection,
  generalWithImageRightSection: GeneralWithImageRightSection,
  imageSection: ImageSection,
  videoSection: VideoSection,
  faqSection: FaqSection,
  tableSection: TableSection,
  boxesSection: BoxesSection,
};

export {
  GeneralSection,
  GeneralWithImageLeftSection,
  GeneralWithImageRightSection,
  ImageSection,
  VideoSection,
  FaqSection,
  TableSection,
  BoxesSection,
};
