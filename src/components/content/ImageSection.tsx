import { Box, Typography } from "@mui/material";
import { ContentBlock } from "../../types/content.types";
import { urlForImage } from "@/lib/sanity/sanityImage";
import Image from "next/image";
import { getStaticSectionColors } from "@/lib/staticColorMap";
import { getImageLayout } from "@/lib/imageLayoutMap";
import styles from "@/ui/styles/ImageSection.module.css";

export function ImageSection({ block }: { block: ContentBlock }) {
  const sectionColors = getStaticSectionColors(block.backgroundColor);
  if (!block.image) return null;

  const layout = block.image.layout || "default";
  const layoutStyles = getImageLayout(layout);

  return (
    <Box
      className={`${styles["image-section"]} content-section-constraint`}
      style={
        {
          "--section-background": sectionColors.background,
        } as React.CSSProperties
      }
    >
      {block.heading && (
        <Typography
          variant="h4"
          component="h4"
          className={styles["image-section__heading"]}
        >
          {block.heading}
        </Typography>
      )}

      <Box
        className={`${styles["image-section__image-wrap"]}`}
        style={layoutStyles as React.CSSProperties}
      >
        <Image
          src={urlForImage(block.image).auto("format").url()}
          alt={block.image.alt || ""}
          fill
          style={{ objectFit: "contain" }}
        />
      </Box>

      {block.image.caption && (
        <Typography
          variant="caption"
          className={styles["image-section__caption"]}
        >
          {block.image.caption}
        </Typography>
      )}

      {block.description && (
        <Typography
          variant="body2"
          className={styles["image-section__description"]}
        >
          {block.description}
        </Typography>
      )}
    </Box>
  );
}
