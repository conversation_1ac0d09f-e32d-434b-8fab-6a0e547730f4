import { Box, Typography, Grid } from "@mui/material";
import { PortableText } from "@portabletext/react";
import { ContentBlock } from "../../types/content.types";
import { urlForImage } from "@/lib/sanity/sanityImage";
import Image from "next/image";
import { portableTextComponents } from "@/lib/sanity/portableTextComponents";
import { getStaticSectionColors } from "@/lib/staticColorMap";
import styles from "@/ui/styles/GeneralWithImageLeftSection.module.css";

export function GeneralWithImageLeftSection({
  block,
}: {
  block: ContentBlock;
}) {
  const sectionColors = getStaticSectionColors(block.backgroundColor);

  return (
    <Box
      className={`${styles["general-left"]}`}
      style={
        {
          "--section-background": sectionColors.background,
        } as React.CSSProperties
      }
    >
      {block.heading && (
        <Typography
          variant="h4"
          component="h4"
          className={styles["general-left__heading"]}
        >
          {block.heading}
        </Typography>
      )}

      <Grid container spacing={{ xs: 4, sm: 4, md: 8 }}>
        {block.image && (
          <Grid size={{ xs: 12, md: 6 }}>
            <Box className={styles["general-left__image-wrap"]}>
              <Image
                src={urlForImage(block.image)
                  .width(1600)
                  .height(1200)
                  .fit("crop")
                  .url()}
                alt={block.image.alt || ""}
                fill
                sizes="(max-width: 600px) 100vw, (max-width: 1200px) 50vw,600px"
                quality={85}
                style={{ objectFit: "cover" }}
              />
            </Box>

            {block.image.caption && (
              <Typography
                variant="caption"
                className={styles["general-left__caption"]}
              >
                {block.image.caption}
              </Typography>
            )}
          </Grid>
        )}

        <Grid size={{ xs: 12, md: 6 }}>
          {block.content && (
            <Box className={`${styles["general-left__content"]} prose-text`}>
              <div>
                <PortableText
                  value={block.content}
                  components={portableTextComponents}
                />
              </div>
            </Box>
          )}
        </Grid>
      </Grid>
    </Box>
  );
}
