import { urlForImage } from "@/lib/sanity/sanityImage";
import { Paper, Box, Typography } from "@mui/material";
import Image from "next/image";
import { SanityBox } from "@/types/content.types";
import styles from "@/ui/styles/BoxCard.module.css";

interface BoxCardProps {
  box: SanityBox;
  boxBgColor: string;
  textColor: string;
  darkerBoxBgColor: string;
}

export default function BoxCard({
  box,
  boxBgColor,
  textColor,
  darkerBoxBgColor,
}: BoxCardProps) {
  return (
    <Paper
      elevation={2}
      className={`${styles["box-card"]} ${box.link ? styles["box-card--clickable"] : ""}`}
      style={
        {
          "--box-bg-color": boxBgColor,
          "--text-color": textColor,
          "--darker-box-bg-color": darkerBoxBgColor,
        } as React.CSSProperties
      }
    >
      {box.icon && (
        <Box className={styles["box-card__icon"]}>
          <Image
            src={urlForImage(box.icon).width(70).height(70).url()}
            alt={box.heading || "icon"}
            width={70}
            height={70}
            aria-hidden={!!box.heading}
          />
        </Box>
      )}

      {box.heading && (
        <Typography variant="h6" className={styles["box-card__heading"]}>
          {box.heading}
        </Typography>
      )}

      {box.text && (
        <Typography variant="body2" className={styles["box-card__text"]}>
          {box.text}
        </Typography>
      )}
    </Paper>
  );
}
