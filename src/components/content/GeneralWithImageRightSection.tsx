import { Box, Typography, Grid } from "@mui/material";
import { PortableText } from "@portabletext/react";
import { ContentBlock } from "../../types/content.types";
import { urlForImage } from "@/lib/sanity/sanityImage";
import Image from "next/image";
import { portableTextComponents } from "@/lib/sanity/portableTextComponents";
import { getStaticSectionColors } from "@/lib/staticColorMap";
import styles from "@/ui/styles/GeneralWithImageRightSection.module.css";

export function GeneralWithImageRightSection({
  block,
}: {
  block: ContentBlock;
}) {
  const sectionColors = getStaticSectionColors(block.backgroundColor);

  return (
    <Box
      className={styles["general-right"]}
      style={
        {
          "--section-background": sectionColors.background,
        } as React.CSSProperties
      }
    >
      {block.heading && (
        <Typography
          variant="h4"
          component="h4"
          className={styles["general-right__heading"]}
        >
          {block.heading}
        </Typography>
      )}

      <Grid container spacing={{ xs: 3, sm: 3, md: 3 }}>
        <Grid size={{ xs: 12, md: 6 }} order={{ xs: 2, sm: 2, md: 1 }}>
          {block.content && (
            <Box className={`${styles["general-right__content"]} prose-text`}>
              <div>
                <PortableText
                  value={block.content}
                  components={portableTextComponents}
                />
              </div>
            </Box>
          )}
        </Grid>

        {block.image && (
          <Grid size={{ xs: 12, md: 6 }} order={{ xs: 1, sm: 1, md: 2 }}>
            <Box className={styles["general-right__image-wrap"]}>
              <Image
                src={urlForImage(block.image)
                  .width(1600)
                  .height(1200)
                  .fit("crop")
                  .url()}
                alt={block.image.alt || ""}
                fill
                sizes="(max-width: 600px) 100vw, (max-width: 1200px) 50vw, 600px"
                quality={85}
                style={{ objectFit: "cover" }}
              />
            </Box>

            {block.image.caption && (
              <Typography
                variant="caption"
                className={styles["general-right__caption"]}
              >
                {block.image.caption}
              </Typography>
            )}
          </Grid>
        )}
      </Grid>
    </Box>
  );
}
