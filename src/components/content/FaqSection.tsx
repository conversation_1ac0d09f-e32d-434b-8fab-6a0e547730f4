import {
  Box,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Container,
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { ContentBlock } from '../../types/content.types';
import { getStaticSectionColors } from '@/lib/staticColorMap';
import styles from '@/ui/styles/FaqSection.module.css';

export function FaqSection({ block }: { block: ContentBlock }) {
  const sectionColors = getStaticSectionColors(block.backgroundColor);

  if (!block.faqs || block.faqs.length === 0) return null;

  return (
    <Box
      className={styles['faq-section']}
      style={
        {
          '--section-background': sectionColors.background,
        } as React.CSSProperties
      }
    >
      <Container maxWidth="md">
        {block.heading && (
          <Box className={styles['faq-section__header']}>
            <Typography variant="h4" className={styles['faq-section__heading']}>
              {block.heading}
            </Typography>
            <Typography
              variant="body1"
              className={styles['faq-section__subheading']}
            >
              Find answers to the most commonly asked questions
            </Typography>
          </Box>
        )}

        <Box className={styles['faq-section__list']}>
          {block.faqs.map((faq, index) => (
            <Accordion
              key={faq._key}
              elevation={0}
              variant="outlined"
              className={styles['faq-section__item']}
            >
              <AccordionSummary
                expandIcon={
                  <Box className={styles['faq-section__expand']}>
                    <ExpandMoreIcon
                      className={styles['faq-section__expand-icon']}
                    />
                  </Box>
                }
                aria-controls={`faq-${faq._key}-content`}
                id={`faq-${faq._key}-header`}
                className={styles['faq-section__summary']}
              >
                <Box className={styles['faq-section__summary-content']}>
                  <Box className={styles['faq-section__badge']}>
                    {String(index + 1).padStart(2, '0')}
                  </Box>
                  <Typography
                    variant="h5"
                    className={styles['faq-section__question']}
                  >
                    {faq.question}
                  </Typography>
                </Box>
              </AccordionSummary>

              <AccordionDetails className={styles['faq-section__details']}>
                <Box className={styles['faq-section__answer-wrap']}>
                  <Typography
                    variant="body2"
                    className={styles['faq-section__answer']}
                  >
                    {faq.answer}
                  </Typography>
                </Box>
              </AccordionDetails>
            </Accordion>
          ))}
        </Box>
      </Container>
    </Box>
  );
}
