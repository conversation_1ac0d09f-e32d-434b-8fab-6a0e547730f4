import {
  Box,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { ContentBlock } from '../../types/content.types';
import { useSectionColors } from '@/hooks/useColorTheme';

export function FaqSection({ block }: { block: ContentBlock }) {
  const sectionBackgroundColor = useSectionColors(block.backgroundColor);

  if (!block.faqs || block.faqs.length === 0) return null;

  return (
    <Box sx={{ mb: 4, background: sectionBackgroundColor.background }}>
      {block.heading && (
        <Typography textAlign="center" variant="h4" sx={{ mb: 2 }}>
          {block.heading}
        </Typography>
      )}
      <Box>
        {block.faqs.map((faq) => (
          <Accordion key={faq._key} sx={{ mb: 1 }}>
            <AccordionSummary
              expandIcon={<ExpandMoreIcon />}
              aria-controls={`faq-${faq._key}-content`}
              id={`faq-${faq._key}-header`}
            >
              <Typography variant="body1" fontWeight="medium">
                {faq.question}
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body1">{faq.answer}</Typography>
            </AccordionDetails>
          </Accordion>
        ))}
      </Box>
    </Box>
  );
}
