import { ContentBlock } from "../../types/content.types";
import Link from "next/link";
import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import { darken } from "@mui/material/styles";
import {
  getStaticSectionColors,
  getStaticBoxColors,
} from "@/lib/staticColorMap";
import BoxCard from "./BoxCard";
import styles from "@/ui/styles/BoxesSection.module.css";

interface Props {
  block: ContentBlock;
}

export default function BoxesSection({ block }: Props) {
  const sectionColors = getStaticSectionColors(block.backgroundColor);
  const boxColors = getStaticBoxColors(block.boxBackgroundColor);

  const darkerBoxBgColor = darken(boxColors.background || "#fff", 0.2);

  const boxes = block.boxes || [];
  const boxCount = boxes.length;

  const isNarrow = boxCount === 2 || boxCount === 4;

  return (
    <Box
      component="section"
      className={`${styles["boxes-section"]} content-section-constraint ${
        isNarrow ? styles["boxes-section--narrow"] : ""
      }`.trim()}
      style={
        {
          "--section-background": sectionColors.background,
          "--section-text": sectionColors.text,
        } as React.CSSProperties
      }
    >
      <Grid
        container
        spacing={3}
        className={`${styles["boxes-section__grid"]} ${styles["boxes-section__grid--centered"]}`}
      >
        {boxes.map((box) => (
          <Grid
            key={box._key}
            size={{ xs: 12, sm: 6, md: 4 }}
            className={styles["boxes-section__col"]}
          >
            {box.link ? (
              <Link
                href={box.link}
                aria-label={box.heading || "Box link"}
                className={styles["boxes-section__link"]}
              >
                <BoxCard
                  box={box}
                  boxBgColor={boxColors.background}
                  textColor={boxColors.text}
                  darkerBoxBgColor={darkerBoxBgColor}
                />
              </Link>
            ) : (
              <BoxCard
                box={box}
                boxBgColor={boxColors.background}
                textColor={boxColors.text}
                darkerBoxBgColor={darkerBoxBgColor}
              />
            )}
          </Grid>
        ))}
      </Grid>
    </Box>
  );
}
