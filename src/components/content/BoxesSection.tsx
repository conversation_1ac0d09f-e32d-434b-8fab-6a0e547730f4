'use client';

import { urlForImage } from '@/lib/sanity/sanityImage';
import { ContentBlock } from '../../types/content.types';
import Image from 'next/image';
import Link from 'next/link';
import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import { useTheme } from '@mui/material/styles';
import { getColorMap } from '@/lib/colorMap.ts';
import { Box as BoxType } from '@/types/content.types';

export type BoxBackgroundColorKey =
  | 'primary'
  | 'secondary'
  | 'light'
  | 'dark'
  | 'success'
  | 'error'
  | 'warning';

interface Props {
  block: ContentBlock;
}

export default function BoxesSection({ block }: Props) {
  const theme = useTheme();

  const boxBackgroundColorKey: BoxBackgroundColorKey =
    (block.boxBackgroundColor as BoxBackgroundColorKey) || 'light';

  const colorMap = getColorMap(theme);

  const boxBgColor = colorMap[boxBackgroundColorKey]?.background || '#f5f5f5';
  const textColor =
    colorMap[boxBackgroundColorKey]?.text || theme.palette.text.primary;

  const boxes = block.boxes || [];

  return (
    <Box
      component="section"
      sx={{
        backgroundColor: '#ffffff',
        py: 4,
        px: 2,
      }}
    >
      <Grid container spacing={3}>
        {boxes.map((box: BoxType) => {
          const content = (
            <Paper
              elevation={3}
              sx={{
                bgcolor: boxBgColor,
                color: textColor,
                p: 2,
                textAlign: box.boxContentAlignment || 'center',
                minHeight: { xs: '250px', sm: '300px' },
              }}
            >
              {box.icon && (
                <Box sx={{ mb: 2 }}>
                  <Image
                    src={urlForImage(box.icon).width(80).height(80).url()}
                    alt={box.heading || ''}
                    width={80}
                    height={80}
                  />
                </Box>
              )}
              {box.heading && (
                <Typography
                  variant="h6"
                  component="h3"
                  gutterBottom
                  sx={{ fontWeight: 'bold' }}
                >
                  {box.heading}
                </Typography>
              )}
              {box.text && <Typography variant="body2">{box.text}</Typography>}
              {box.link && (
                <Link
                  href={box.link}
                  style={{
                    textDecoration: 'underline',
                    paddingTop: '10px',
                    display: 'block',
                  }}
                >
                  <Typography variant="body2">
                    {box.linkContent ? box.linkContent : 'Read more'}
                  </Typography>
                </Link>
              )}
            </Paper>
          );

          return (
            <Grid size={{ xs: 12, sm: 6, md: 4 }} key={box._key}>
              {box.link ? (
                <Link
                  href={box.link}
                  style={{
                    textDecoration: 'none',
                    color: 'inherit',
                    display: 'block',
                    height: '100%',
                  }}
                >
                  {content}
                </Link>
              ) : (
                content
              )}
            </Grid>
          );
        })}
      </Grid>
    </Box>
  );
}
