'use client';

import { urlForImage } from '@/lib/sanity/sanityImage';
import { ContentBlock } from '../../types/content.types';
import Image from 'next/image';
import Link from 'next/link';
import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import { Box as BoxType } from '@/types/content.types';
// import { SectionWrapper } from '@/components/common/SectionWrapper';
import { useBoxColors, useSectionColors } from '@/hooks/useColorTheme';

interface Props {
  block: ContentBlock;
}

export default function BoxesSection({ block }: Props) {
  // Get colors for the individual boxes
  const boxBackgroundColor = useBoxColors(block.boxBackgroundColor);
  const sectionBackgroundColor = useSectionColors(block.backgroundColor);

  const boxes = block.boxes || [];
  const boxCount = boxes.length;

  // Calculate container width based on number of boxes
  const getContainerMaxWidth = () => {
    if (boxCount === 2) {
      return { xs: '100%', sm: '100%', md: '66.67%' }; // 2 boxes: 2/3 width on md+
    } else if (boxCount === 4) {
      return { xs: '100%', sm: '100%', md: '66.67%' }; // 4 boxes: 2/3 width on md+
    }
    return '100%'; // Default: full width
  };

  return (
    <Box
      component="section"
      sx={{
        py: 4,
        px: 2,

        background: sectionBackgroundColor.background,
      }}
    >
      {/* <Box> */}
      <Grid
        container
        spacing={3}
        justifyContent={boxCount === 2 || boxCount === 4 ? 'center' : ''}
        sx={{
          maxWidth: getContainerMaxWidth(),
          mx: 'auto',
        }}
      >
        {boxes.map((box: BoxType) => {
          const content = (
            <Paper
              elevation={3}
              sx={{
                bgcolor: boxBackgroundColor.background,
                color: boxBackgroundColor.text,
                p: 2,
                textAlign: box.boxContentAlignment || 'center',
                minHeight: { xs: '250px', sm: '300px' },
              }}
            >
              {box.icon && (
                <Box sx={{ mb: 2 }}>
                  <Image
                    src={urlForImage(box.icon).width(80).height(80).url()}
                    alt={box.heading || ''}
                    width={80}
                    height={80}
                  />
                </Box>
              )}
              {box.heading && (
                <Typography
                  variant="h6"
                  component="h3"
                  gutterBottom
                  sx={{ fontWeight: 'bold' }}
                >
                  {box.heading}
                </Typography>
              )}
              {box.text && <Typography variant="body2">{box.text}</Typography>}
              {box.link && (
                <Link
                  href={box.link}
                  style={{
                    textDecoration: 'underline',
                    paddingTop: '10px',
                    display: 'block',
                  }}
                >
                  <Typography variant="body2">
                    {box.linkContent ? box.linkContent : 'Read more'}
                  </Typography>
                </Link>
              )}
            </Paper>
          );

          return (
            <Grid
              minWidth={{ xs: '250px', sm: '300px' }}
              size={{ xs: 12, sm: 6, md: 4 }}
              key={box._key}
            >
              {box.link ? (
                <Link
                  href={box.link}
                  style={{
                    textDecoration: 'none',
                    color: 'inherit',
                    display: 'block',
                    height: '100%',
                  }}
                >
                  {content}
                </Link>
              ) : (
                content
              )}
            </Grid>
          );
        })}
      </Grid>
    </Box>
  );
}
