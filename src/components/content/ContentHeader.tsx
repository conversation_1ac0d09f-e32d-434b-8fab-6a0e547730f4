import { Box, Typography } from '@mui/material';
import { ContentTemplateContent } from '../../types/content.types';

export function ContentHeader({
  content,
}: {
  content: ContentTemplateContent;
}) {
  return (
    <Box component="header" sx={{ mb: 4 }}>
      <Typography textAlign="center" variant="h2" component="h1" sx={{ mb: 2 }}>
        {content.title}
      </Typography>
      {content.publishedAt && (
        <Typography variant="body2" color="text.secondary">
          Published: {new Date(content.publishedAt).toLocaleDateString()}
        </Typography>
      )}
      <Box sx={{ mt: 1 }}>
        <Box
          component="span"
          sx={{
            display: 'inline-block',
            bgcolor: 'primary.light',
            color: 'primary.dark',
            fontSize: '0.875rem',
            fontWeight: 'medium',
            mr: 1,
            px: 1.5,
            py: 0.5,
            borderRadius: 1,
          }}
        >
          {content.category}
        </Box>
      </Box>
    </Box>
  );
}
