import { Box, Chip, Typography } from "@mui/material";
import { ContentTemplateContent } from "../../types/content.types";
import styles from "@/ui/styles/ContentHeader.module.css";

export function ContentHeader({
  content,
}: {
  content: ContentTemplateContent;
}) {
  return (
    <Box
      component="header"
      className={`${styles["content-header"]} content-section-constraint`}
    >
      <Box className={styles["content-header__inner"]}>
        <Box>
          <Typography
            variant="h3"
            className={`${styles["content-header__title"]} `}
          >
            {content.title}
          </Typography>

          {content.publishedAt && (
            <Chip
              label={`Published ${new Date(content.publishedAt).toLocaleDateString()}`}
              size="small"
              variant="outlined"
              className={styles["content-header__chip"]}
            />
          )}
        </Box>
      </Box>
    </Box>
  );
}
