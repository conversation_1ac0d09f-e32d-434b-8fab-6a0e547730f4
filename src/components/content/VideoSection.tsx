import { Box, Typography } from "@mui/material";
import { ContentBlock } from "../../types/content.types";
import { getStaticSectionColors } from "@/lib/staticColorMap";
import styles from "@/ui/styles/VideoSection.module.css";

export function VideoSection({ block }: { block: ContentBlock }) {
  const sectionColors = getStaticSectionColors(block.backgroundColor);
  if (!block.videoUrl) return null;

  // Extract video ID from YouTube or Vimeo URL
  const getEmbedUrl = (url: string) => {
    const youtubeRegex =
      /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
    const youtubeMatch = url.match(youtubeRegex);
    if (youtubeMatch && youtubeMatch[1])
      return `https://www.youtube.com/embed/${youtubeMatch[1]}`;

    const vimeoRegex =
      /(?:vimeo\.com\/(?:channels\/(?:\w+\/)?|groups\/[^\/]*\/videos\/|album\/\d+\/video\/|)(\d+)(?:$|\/|\?))/;
    const vimeoMatch = url.match(vimeoRegex);
    if (vimeoMatch && vimeoMatch[1])
      return `https://player.vimeo.com/video/${vimeoMatch[1]}`;

    return url; // fallback
  };

  const embedUrl = getEmbedUrl(block.videoUrl);

  return (
    <Box
      className={`${styles["video-section"]} content-section-constraint`}
      style={
        {
          "--section-background": sectionColors.background,
        } as React.CSSProperties
      }
    >
      {block.heading && (
        <Typography variant="h4" className={styles["video-section__heading"]}>
          {block.heading}
        </Typography>
      )}

      <Box className={styles["video-section__frame-wrap"]}>
        <iframe
          src={embedUrl}
          title={block.heading || "Video content"}
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
          className={styles["video-section__iframe"]}
        />
      </Box>

      {block.description && (
        <Typography
          variant="body2"
          className={styles["video-section__description"]}
        >
          {block.description}
        </Typography>
      )}
    </Box>
  );
}
