import { Box, Typography, Container } from '@mui/material';
import { PortableText } from '@portabletext/react';
import { ContentBlock } from '../../types/content.types';
import { portableTextComponents } from '@/lib/sanity/portableTextComponents';
import { getStaticSectionColors } from '@/lib/staticColorMap';
import styles from '@/ui/styles/GeneralSection.module.css';

export function GeneralSection({ block }: { block: ContentBlock }) {
  const sectionColors = getStaticSectionColors(block.backgroundColor);

  return (
    <Box
      className={`${styles['general-section']} content-section-constraint`}
      style={
        {
          '--section-background': sectionColors.background,
          '--section-text-color': sectionColors.text || 'var(--text-secondary)',
        } as React.CSSProperties
      }
    >
      <Container maxWidth="md" className={styles['general-section__container']}>
        <Box>
          {block.heading && (
            <Typography
              variant="h4"
              className={styles['general-section__heading']}
            >
              {block.heading}
            </Typography>
          )}

          {block.content && (
            <Box className={`${styles['general-section__content']} prose-text`}>
              <PortableText
                value={block.content}
                components={portableTextComponents}
              />
            </Box>
          )}
        </Box>
      </Container>
    </Box>
  );
}
