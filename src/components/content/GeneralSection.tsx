import { Box, Typography } from '@mui/material';
import { PortableText } from '@portabletext/react';
import { ContentBlock } from '../types';
import { portableTextComponents } from '@/lib/sanity/portableTextComponents';

export function GeneralSection({ block }: { block: ContentBlock }) {
  return (
    <Box sx={{ mb: 4 }}>
      {block.heading && (
        <Typography variant='h4' component='h2' sx={{ mb: 2 }}>
          {block.heading}
        </Typography>
      )}
      {block.content && (
        <Box className='prose max-w-none'>
          <PortableText value={block.content} components={portableTextComponents} />
        </Box>
      )}
    </Box>
  );
}
