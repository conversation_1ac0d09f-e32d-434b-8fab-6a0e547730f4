import { Box, Typography } from '@mui/material';
import { PortableText } from '@portabletext/react';
import { ContentBlock } from '../../types/content.types';
import { portableTextComponents } from '@/lib/sanity/portableTextComponents';
import { getStaticSectionColors } from '@/lib/staticColorMap';

export function GeneralSection({ block }: { block: ContentBlock }) {
  const sectionColors = getStaticSectionColors(block.backgroundColor);

  return (
    <Box sx={{ mb: 4, background: sectionColors.background }}>
      {block.heading && (
        <Typography variant="h4" component="h2" sx={{ mb: 2 }}>
          {block.heading}
        </Typography>
      )}
      {block.content && (
        <Box className="prose max-w-none">
          <PortableText
            value={block.content}
            components={portableTextComponents}
          />
        </Box>
      )}
    </Box>
  );
}
