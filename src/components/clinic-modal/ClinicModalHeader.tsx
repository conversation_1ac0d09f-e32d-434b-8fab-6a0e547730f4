import { Clinic } from '@/types/clinic.type';
import { Box, IconButton, Theme, Typography } from '@mui/material';
import { Close } from '@mui/icons-material';
import Image from 'next/image';
import { IsMobile } from '@/lib/utils';

interface ClinicModalHeaderProps {
  clinic: Clinic;
  onClose: () => void;
  theme: Theme;
}

const ClinicModalHeader = ({
  clinic,
  onClose,
  theme,
}: ClinicModalHeaderProps) => {
  const isMobile = IsMobile();

  return (
    <Box
      sx={{
        py: 1,
        px: { xs: 2, md: 4 },
        border: `2px solid ${theme.palette.grey[200]}`,
        position: 'relative',
        bgcolor: theme.palette.grey[100],
        backdropFilter: 'blur(10px)',
      }}
    >
      <IconButton
        onClick={onClose}
        sx={{
          position: 'absolute',
          top: isMobile ? 3 : 5,
          right: isMobile ? 2 : 5,
          color: theme.palette.grey[600],
          bgcolor: 'white',
          border: `1px solid ${theme.palette.grey[500]}`,
          width: 30,
          height: 30,
          '&:hover': {
            bgcolor: theme.palette.grey[200],
            color: theme.palette.grey[800],
            transform: 'scale(1.05)',
          },
          transition: 'all 0.2s ease-in-out',
        }}
      >
        <Close />
      </IconButton>

      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
        <Box
          sx={{
            width: '5rem',
            height: '5rem',
            position: 'relative',
            borderRadius: '0.5rem',
            overflow: 'hidden',
            border: `2px solid ${theme.palette.grey[300]}`,
            flexShrink: 0,
            bgcolor: 'white',
          }}
        >
          <Image
            src={
              clinic.logo ||
              'https://selfpayhealth.s3.eu-west-2.amazonaws.com/logo.png'
            }
            alt={`${clinic.name} logo`}
            fill
            style={{ objectFit: 'contain', padding: '10px' }}
          />
        </Box>

        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            flex: 1,
            minWidth: 0,
          }}
        >
          <Typography
            sx={{
              fontSize: { xs: '20px', sm: '36px', lineHeight: 1.3 },
            }}
            component="h4"
            fontWeight={600}
          >
            {clinic.name}
          </Typography>
          <Typography
            variant="body1"
            fontWeight={600}
            sx={{
              flexShrink: 0,
              fontSize: { xs: '16px', sm: '24px' },
              paddingTop: { xs: '0.2rem', sm: '0.1rem' },
            }}
          >
            Intital consultations: {clinic.consultationRate || '£200'}
          </Typography>
        </Box>
      </Box>
    </Box>
    // </Box>
  );
};

export default ClinicModalHeader;
