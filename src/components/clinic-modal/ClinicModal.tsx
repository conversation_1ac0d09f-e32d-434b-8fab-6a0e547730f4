'use client';

import {
  Mo<PERSON>,
  Box,
  Typography,
  Chip,
  useTheme,
  Divider,
  Link,
} from '@mui/material';
import PersonIcon from '@mui/icons-material/Person';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import InfoIcon from '@mui/icons-material/Info';
import BusinessIcon from '@mui/icons-material/Business';
import { Clinic } from '../../types/clinic.type';
import { IsMobile } from '../../lib/utils';
import ClinicModalHeader from './ClinicModalHeader';
import ClinicModalFooter from './ClinicModalFooter';
import { getClinicInfo } from '@/hooks/use-clinic-modal';
import { SearchBarParams } from '@/lib/api/types/searchbar-params.type';
import React from 'react';

interface ClinicDetailModalProps {
  clinic: Clinic;
  open: boolean;
  index: number;
  searchParams: SearchBarParams;
  searchSource: 'manual' | 'ai';
  onClose: () => void;
}

const ClinicModal = ({
  index,
  clinic,
  open,
  onClose,
  searchParams,
  searchSource,
}: ClinicDetailModalProps) => {
  const theme = useTheme();
  const { distanceInMiles, displayDescription } = getClinicInfo(clinic);
  const isMobile = IsMobile();

  return (
    <Modal
      open={open}
      onClose={onClose}
      aria-labelledby="clinic-detail-modal"
      aria-describedby="detailed-information-about-clinic"
      sx={{
        backdropFilter: 'blur(15px)',
        ...(isMobile && {
          display: 'flex',
          alignItems: 'flex-end',
        }),
      }}
    >
      <Box
        sx={{
          position: 'absolute',
          ...(isMobile
            ? {
                bottom: 0,
                left: 0,
                right: 0,
                width: '100%',
                maxHeight: '86vh',
                borderRadius: '16px 16px 0 0',
              }
            : {
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                width: '1000px',
                maxHeight: '88vh',
                borderRadius: '12px',
              }),
          bgcolor: 'background.paper',
          overflow: 'hidden',
          boxShadow: 24,
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <ClinicModalHeader clinic={clinic} onClose={onClose} theme={theme} />

        <Box sx={{ flex: 1, overflowY: 'auto', p: { xs: 2, sm: 4 } }}>
          <Box sx={{ mb: 5 }}>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                mb: 3,
              }}
            >
              <Divider
                sx={(theme) => ({
                  flex: 1,
                  mr: 2,
                  height: 2,
                  borderRadius: '1rem',
                  bgcolor: 'transparent',
                  border: 'none',
                  backgroundImage: `linear-gradient(to left, ${theme.palette.primary.main} 0%, #fff 95%)`,
                })}
              />{' '}
              <Typography
                variant="h6"
                fontWeight={700}
                color="primary.main"
                sx={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  gap: 1,
                  px: 2,
                  textTransform: 'uppercase',
                  letterSpacing: 1,
                  fontSize: '1.2rem',
                }}
              >
                <BusinessIcon fontSize="small" />
                About
              </Typography>
              <Divider
                sx={(theme) => ({
                  flex: 1,
                  mr: 2,
                  height: 2,
                  bgcolor: 'transparent',
                  border: 'none',
                  borderRadius: '1rem',
                  backgroundImage: `linear-gradient(to right, ${theme.palette.primary.main} 0%, #fff 95%)`,
                })}
              />
            </Box>

            <Typography
              variant="body1"
              color="text.secondary"
              sx={{
                lineHeight: 1.6,
                p: isMobile ? '1rem 0.5rem' : '1rem',
                mr: isMobile ? 0 : { sm: '1rem', md: '5rem' },
                ml: isMobile ? 0 : { sm: '1rem', md: '5rem' },
              }}
            >
              {displayDescription}
            </Typography>
          </Box>

          {/* Info Section */}
          <Box sx={{ mb: 1 }}>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                mb: isMobile ? 2 : 3,
              }}
            >
              <Divider
                sx={(theme) => ({
                  flex: 1,
                  mr: 2,
                  height: 2,
                  borderRadius: '1rem',
                  bgcolor: 'transparent',
                  border: 'none',
                  backgroundImage: `linear-gradient(to left, ${theme.palette.primary.main} 0%, #fff 95%)`,
                })}
              />
              <Typography
                variant="h6"
                color="primary.main"
                fontWeight={700}
                sx={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  gap: 1,
                  px: 2,
                  textTransform: 'uppercase',
                  letterSpacing: 1,
                  fontSize: '1.2rem',
                }}
              >
                <InfoIcon fontSize="small" />
                Info
              </Typography>
              <Divider
                sx={(theme) => ({
                  flex: 1,
                  mr: 2,
                  height: 2,
                  borderRadius: '1rem',
                  bgcolor: 'transparent',
                  border: 'none',
                  backgroundImage: `linear-gradient(to right, ${theme.palette.primary.main} 0%, #fff 95%)`,
                })}
              />
            </Box>

            <Box
              sx={{
                display: 'flex',
                flexDirection: isMobile ? 'column' : 'row',
                gap: isMobile ? 3 : 4,
                mb: 4,
              }}
            >
              <Box
                sx={{
                  width: isMobile ? '100%' : '50%',
                  p: isMobile ? 2.5 : 3,
                  borderRadius: '12px',
                  border: `1px solid ${theme.palette.grey[200]}`,
                  bgcolor: 'white',
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    mb: 2,
                  }}
                >
                  <LocationOnIcon
                    sx={{
                      color: theme.palette.primary.main,
                      fontSize: isMobile ? 20 : 22,
                      mr: 1.5,
                    }}
                  />
                  <Typography
                    variant="body1"
                    sx={{
                      fontWeight: 600,
                      color: theme.palette.text.primary,
                    }}
                  >
                    Location
                  </Typography>
                </Box>

                {clinic && clinic.address && (
                  <>
                    <Box sx={{ ml: isMobile ? 2 : 4 }}>
                      <Typography
                        variant="body1"
                        color="text.primary"
                        sx={{
                          mb: 0.5,
                        }}
                      >
                        {[
                          clinic.address.addressLine1,
                          clinic.address.addressLine2,
                          clinic.address.addressLine3,
                          clinic.address.townCity,
                          clinic.address.postcode,
                        ]
                          .filter(Boolean) // Remove falsy values (null, undefined, empty strings)
                          .map((line, index, array) => (
                            <React.Fragment key={index}>
                              {line}
                              {index < array.length - 1 && <br />}
                            </React.Fragment>
                          ))}
                      </Typography>
                      <Typography
                        variant="body1"
                        color="text.primary"
                        sx={{
                          mb: 0.5,
                        }}
                      ></Typography>

                      {distanceInMiles && (
                        <Typography
                          variant="body1"
                          color="text.secondary"
                          sx={{
                            mt: 1,
                            fontSize: '1rem',
                            fontWeight: 500,
                          }}
                        >
                          {distanceInMiles} miles away
                        </Typography>
                      )}
                    </Box>

                    {clinic?.address?.postcode && (
                      <Box sx={{ mt: 2, ml: isMobile ? 2 : 4 }}>
                        <Link
                          href={`https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(
                            `${clinic.address.addressLine1 || ''} ${clinic.address.postcode}`
                          )}`}
                          target="_blank"
                          underline="hover"
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            fontSize: 15,
                            fontWeight: 500,
                            color: theme.palette.primary.main,
                            ...(isMobile && {
                              py: 1,
                              minHeight: 44,
                            }),
                          }}
                        >
                          View on Google Maps
                        </Link>
                      </Box>
                    )}
                  </>
                )}
              </Box>

              {!!clinic.consultants?.length && (
                <Box
                  sx={{
                    width: isMobile ? '100%' : '50%',
                    p: isMobile ? 2.5 : 3,
                    borderRadius: '8px',
                    border: `1px solid ${theme.palette.grey[200]}`,
                    bgcolor: 'white',
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <PersonIcon
                      sx={{
                        color: theme.palette.primary.main,
                        fontSize: isMobile ? 18 : 20,
                        mr: 1.5,
                      }}
                    />
                    <Typography
                      variant="body1"
                      sx={{
                        fontWeight: 600,
                        color: theme.palette.text.primary,
                      }}
                    >
                      Clinic Consultants ({clinic.consultants.length})
                    </Typography>
                  </Box>

                  <Box
                    sx={{
                      display: 'flex',
                      flexWrap: 'wrap',
                      gap: isMobile ? 0.5 : 1,
                      ml: isMobile ? 3 : 4,
                    }}
                  >
                    {clinic.consultants.slice(0, 8).map((consultant, i) => {
                      const fullName = [
                        consultant.title,
                        consultant.firstName,
                        consultant.lastName,
                      ]
                        .filter(Boolean)
                        .join(' ');

                      return (
                        <Chip
                          key={`${fullName || consultant}-${i}`}
                          label={fullName || String(consultant)}
                          size="small"
                          sx={{
                            bgcolor: theme.palette.grey[100],
                            color: theme.palette.text.primary,
                            fontSize: '0.9rem',
                            height: isMobile ? '26px' : '28px',
                            '&:hover': {
                              bgcolor: theme.palette.grey[200],
                            },
                          }}
                        />
                      );
                    })}
                  </Box>
                </Box>
              )}
            </Box>
          </Box>
        </Box>

        <ClinicModalFooter
          searchParams={searchParams}
          searchSource={searchSource}
          clinic={clinic}
          theme={theme}
          index={index}
        />
      </Box>
    </Modal>
  );
};

export default ClinicModal;
