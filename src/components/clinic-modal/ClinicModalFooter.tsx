import { IsMobile } from '@/lib/utils';
import { Clinic } from '@/types/clinic.type';
import { Box, Button, Theme } from '@mui/material';
import PhoneIcon from '@mui/icons-material/Phone';
import LanguageIcon from '@mui/icons-material/Language';
import { useClinicLeadActions } from '@/hooks/use-clinic-modal';
import TodayIcon from '@mui/icons-material/Today';
import { useState } from 'react';
import { SearchBarParams } from '@/lib/api/types/searchbar-params.type';

interface ClinicModalFooterProps {
  clinic: Clinic;
  theme: Theme;
  index: number;
  searchParams: SearchBarParams;
  searchSource: 'manual' | 'ai';
}

const ClinicModalFooter = ({
  clinic,
  theme,
  index,
  searchParams,
  searchSource,
}: ClinicModalFooterProps) => {
  const { onWebsiteClick, onPhoneClick } = useClinicLeadActions(
    clinic,
    index,
    searchParams,
    searchSource
  );

  const [showPhoneNumber, setShowPhoneNumber] = useState(false);
  const isMobile = IsMobile();

  const handlePhoneClick = () => {
    if (!showPhoneNumber) {
      setShowPhoneNumber(true);
    }
    onPhoneClick();
  };

  return (
    <Box
      sx={{
        // p: isMobile ? 0.5 : 1.5,
        px: { xs: 2, sm: 5 },
        py: { xs: 1, sm: 2 },
        borderTop: `1px solid ${theme.palette.grey[200]}`,
        bgcolor: theme.palette.grey[100],
        pb: isMobile ? 0.5 : 2,
        position: 'relative',
      }}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          gap: isMobile ? 1.5 : 4,
          justifyContent: 'center',
          alignItems: 'stretch',
        }}
      >
        <Button
          variant="contained"
          size="medium"
          sx={{
            background: {
              xs: 'grey',
              sm: `linear-gradient(90deg, ${theme.palette.secondary.main}, ${theme.palette.secondary.light})`,
            },
            color: '#fff',
            flex: 1,
            borderRadius: '12px',
            fontSize: { xs: '0.9rem', sm: '1rem' },
            fontWeight: 600,
            display: 'flex',
            gap: 0.5,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: { xs: '44px', sm: '48px' },
            '&:hover': {
              background: `linear-gradient(135deg, ${theme.palette.secondary.main}, ${theme.palette.secondary.dark})`,
              transform: 'translateY(-1px)',
            },
            transition: 'all 0.2s ease-in-out',
            cursor: 'not-allowed',
          }}
        >
          <TodayIcon sx={{ fontSize: { xs: 16, sm: 20 }, color: 'white' }} />
          {isMobile ? 'Book' : 'Book Today'}
        </Button>

        {!!clinic.linkToWebsite && (
          <Button
            onClick={onWebsiteClick}
            target="_blank"
            rel="noopener noreferrer"
            variant="outlined"
            href={`${clinic.linkToWebsite as string}/?utm_source=selfpayhealth.com&utm_medium=referral&utm_campaign=${searchParams.searchTerm?.name}`}
            sx={{
              borderColor: theme.palette.primary.main,
              color: theme.palette.primary.main,
              backgroundColor: 'white',
              flex: 1,
              borderRadius: '12px',
              fontSize: { xs: '0.9rem', sm: '1rem' },
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
              gap: 0.5,
              fontWeight: 600,
              minHeight: { xs: '44px', sm: '48px' },
              '&:hover': {
                borderColor: theme.palette.primary.dark,
                backgroundColor: theme.palette.primary.light,
                transform: 'translateY(-1px)',
              },
              transition: 'all 0.2s ease-in-out',
            }}
          >
            <LanguageIcon
              sx={{
                color: theme.palette.primary.main,
                fontSize: { xs: 16, sm: 20 },
              }}
            />
            {isMobile ? 'Web' : 'Visit website'}
          </Button>
        )}

        {!!clinic.telephoneNumber && (
          <Button
            onClick={handlePhoneClick}
            variant="outlined"
            size="medium"
            sx={{
              borderColor: theme.palette.primary.main,
              color: theme.palette.primary.main,
              backgroundColor: 'white',
              flex: 1,
              borderRadius: '12px',
              fontSize: { xs: '0.9rem', sm: '1rem' },
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
              gap: 0.5,
              fontWeight: 600,
              minHeight: { xs: '44px', sm: '48px' },
              '&:hover': {
                borderColor: theme.palette.primary.dark,
                backgroundColor: theme.palette.primary.light,
                transform: 'translateY(-1px)',
              },
              transition: 'all 0.2s ease-in-out',
              lineHeight: 1.2,
            }}
          >
            <PhoneIcon sx={{ fontSize: { xs: 16, sm: 20 } }} />
            {isMobile
              ? showPhoneNumber
                ? clinic.telephoneNumber
                : 'Call'
              : showPhoneNumber
                ? clinic.telephoneNumber
                : 'See Phone'}
          </Button>
        )}
      </Box>
    </Box>
  );
};

export default ClinicModalFooter;
