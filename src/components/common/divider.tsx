// src/components/common/Divider.tsx
import { Box } from '@mui/material';

interface DividerProps {
  colorScheme: 'grey' | 'gold';
}

export function Divider({ colorScheme }: DividerProps) {
  const colors = {
    gold: {
      // gradient: `linear-gradient(90deg, transparent 0%, #FFF1B3 30%, #99916C 100%)`,
      gradient: `linear-gradient(90deg, #FFF1B3 0%,  #99916C 100%)`,
      center: '#26827D',
    },
    grey: {
      // gradient: `linear-gradient(90deg, transparent 0%, #e0e0e0 30%, #999999 100%)`,
      gradient: `linear-gradient(90deg, #FFFFFF 0%, #999999 100%)`,
      center: '#26827D',
    },
  };

  const SingleLine = () => (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        width: '100%',
        mx: 'auto',
        gap: '15px', // Space between elements
      }}
    >
      {/* Left gold gradient line */}
      <Box
        sx={{
          flex: 1,
          height: '2px',
          background: colors[colorScheme].gradient,
        }}
      />

      {/* Center green section */}
      <Box
        sx={{
          width: '70px', // Adjust width as needed
          height: '2px',
          backgroundColor: colors[colorScheme].center,
        }}
      />

      {/* Right gold gradient line (reversed) */}
      <Box
        sx={{
          flex: 1,
          height: '2px',
          background: colors[colorScheme].gradient,
          transform: 'scaleX(-1)', // Flip horizontally for symmetry
        }}
      />
    </Box>
  );

  return (
    <Box
      sx={{
        width: '100%',
        my: 4,
        display: 'flex',
        flexDirection: 'column',
        gap: '2px', // 4px vertical separation between the two lines
      }}
    >
      <SingleLine />
      <SingleLine />
    </Box>
  );
}
