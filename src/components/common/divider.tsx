// src/components/common/Divider.tsx
import { Box } from '@mui/material';

interface DividerProps {
  colorScheme: 'grey' | 'gold';
}

export function Divider({ colorScheme }: DividerProps) {
  const dividerStyles = {
    gold: {
      background: `linear-gradient(
        90deg,
        transparent 0%,
        #FFF1B3 20%,
        #99916C 40%,
        #26827D 50%,
        #99916C 60%,
        #FFF1B3 80%,
        transparent 100%
      )`,
    },
    grey: {
      background: `linear-gradient(
        90deg,
        transparent 0%,
        #e0e0e0 20%,
        #999999 40%,
        #666666 50%,
        #999999 60%,
        #e0e0e0 80%,
        transparent 100%
      )`,
    },
  };

  return (
    <Box
      sx={{
        width: '100%',
        height: '2px',
        background: dividerStyles[colorScheme].background,
        my: 4,
        mx: 'auto',
        maxWidth: '600px', // Adjust based on your design needs
      }}
    />
  );
}
