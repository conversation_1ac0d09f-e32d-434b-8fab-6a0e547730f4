// src/components/common/Divider.tsx
import { Box } from '@mui/material';

interface DividerProps {
  colorScheme: 'grey' | 'gold';
}

export function Divider({ colorScheme }: DividerProps) {
  const colors = {
    gold: {
      gradient: `linear-gradient(90deg, #FFF1B3 0%,  #99916C 100%)`,
      center: '#26827D',
    },
    grey: {
      gradient: `linear-gradient(90deg, #FFFFFF 0%, #999999 100%)`,
      center: '#26827D',
    },
  };

  const SingleLine = () => (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        width: '100%',
        mx: 'auto',
        gap: '15px',
      }}
    >
      {/* Left gold gradient line */}
      <Box
        sx={{
          flex: 1,
          height: '2px',
          background: colors[colorScheme].gradient,
        }}
      />

      {/* Center green section */}
      <Box
        sx={{
          width: '70px',
          height: '2px',
          backgroundColor: colors[colorScheme].center,
        }}
      />

      {/* Right gold gradient line (reversed) */}
      <Box
        sx={{
          flex: 1,
          height: '2px',
          background: colors[colorScheme].gradient,
          transform: 'scaleX(-1)',
        }}
      />
    </Box>
  );

  return (
    <Box
      sx={{
        width: '100%',
        mt: 4,
        mb: 4,
        display: 'flex',
        flexDirection: 'column',
        gap: '2px',
      }}
    >
      <SingleLine />
      <SingleLine />
    </Box>
  );
}
