import React from 'react';
import { Box, SxProps, Theme } from '@mui/material';
import { useSectionColors } from '@/hooks/useColorTheme';

interface SectionWrapperProps {
  children: React.ReactNode;
  backgroundColor?: string;
  component?: React.ElementType;
  sx?: SxProps<Theme>;
  className?: string;
}

/**
 * Reusable section wrapper that handles background colors consistently
 * across all content sections
 */
export function SectionWrapper({
  children,
  backgroundColor,
  component = 'section',
  sx = {},
  className,
  ...props
}: SectionWrapperProps) {
  const colors = useSectionColors(backgroundColor);

  return (
    <Box
      component={component}
      className={className}
      sx={{
        backgroundColor: colors.background,
        color: colors.text,
        py: 4,
        px: 2,
        ...sx,
      }}
      {...props}
    >
      {children}
    </Box>
  );
}
