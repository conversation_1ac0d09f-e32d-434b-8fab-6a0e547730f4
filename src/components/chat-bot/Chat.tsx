'use client';

import type { UIMessage } from 'ai';
import { useChat } from '@ai-sdk/react';
import { Box, Stack, TextField } from '@mui/material';
import { generateUUID } from '../../lib/utils';
import { Messages } from './Messages';
import { SearchTerm } from '@/lib/api/types/search-term.type';
// import { getClinics } from '@/lib/api/clinics';

// TODOS
// I am not sure if this is the best way to handle the args, but it works for now
// fix the persistence of current progress to the user as it resets and isn't good UX

interface SpecialistToolArgs {
  id: string;
  speciality: string;
  postcode?: string;
}

export function Chat({
  id,
  initialMessages,
  handleSearch,
}: {
  id: string;
  initialMessages: Array<UIMessage>;
  handleSearch: ({
    searchTerm,
    postcode,
    distance,
  }: {
    searchTerm: SearchTerm;
    postcode: string | undefined;
    distance: number | undefined;
  }) => void;
}) {
  const { messages, input, status, handleInputChange, handleSubmit } = useChat({
    id,
    initialMessages,
    generateId: generateUUID,
    api: `${process.env.NEXT_PUBLIC_API_URL}/ai/chat`,
    maxSteps: 5,
    onToolCall: async ({ toolCall }) => {
      const { toolName, args } = toolCall;

      // Check if this is our getSpecialist tool to avoid firing in an incorrect scenario
      if (toolName === 'getspecialist') {
        try {
          const specialistArgs = args as SpecialistToolArgs;
          handleSearch({
            searchTerm: {
              type: 'Speciality',
              order: 1,
              id: specialistArgs.id,
              name: specialistArgs.speciality,
              desc: '',
            },
            postcode: specialistArgs.postcode,
            distance: 20,
          });
        } catch (error) {
          console.error('Error processing getspecialist args:', error);
        }
      }
    },
  });
  if (messages.length > 0 && messages.length < 2) {
    window.dataLayer = window.dataLayer || [];
    window.dataLayer.push({
      event: 'ai_chat_initiated',
    });
  }
  // console.log(status);

  return (
    <Box component={'section'} sx={{ marginTop: '2rem' }}>
      <Stack spacing={2}>
        <Messages chatId={id} status={status} messages={messages} />
        <form onSubmit={handleSubmit}>
          <TextField
            fullWidth
            label="Write your query:"
            type="text"
            name="content"
            value={input}
            onChange={handleInputChange}
          />
        </form>
      </Stack>
    </Box>
  );
}
