'use client';

import type { UIMessage } from 'ai';
import { useChat } from '@ai-sdk/react';
import { Box, Stack, TextField, Button, Typography } from '@mui/material';
import { useState, useEffect, useRef } from 'react';
import { generateUUID } from '../../lib/utils';
import { Messages } from './Messages';
// import { getClinics } from '@/lib/api/clinics';

// TODOS
// I am not sure if this is the best way to handle the args, but it works for now
// fix the persistence of current progress to the user as it resets and isn't good UX

// TODO - is this the correct args
interface SpecialistToolArgs {
  searchTerm: string;
  speciality: string;
  id: string;
  postcode: string;
  type: string;
}

export function Chat({
  handleSearch,
  id,
  initialMessages,
}: {
  id: string;
  initialMessages: Array<UIMessage>;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  handleSearch: (params: any) => void;
}) {
  const [pendingAssistantMessage, setPendingAssistantMessage] =
    useState<UIMessage | null>(null);
  const [quickReplies, setQuickReplies] = useState<{
    id: string;
    prompt: string;
    requestId?: string | null;
    options: Array<{ label: string; value: string; requiresInput?: boolean }>;
  } | null>(null);

  const getMessageText = (m: UIMessage) => {
    if (typeof m.content === 'string') return m.content;
    const maybeParts = m as unknown as { parts?: Array<{ text?: string }> };
    if (Array.isArray(maybeParts.parts))
      return maybeParts.parts.map((p) => p.text || '').join(' ');
    return '';
  };

  const downloadConversation = () => {
    const all = [
      ...messages,
      ...(pendingAssistantMessage ? [pendingAssistantMessage] : []),
    ];
    const lines: string[] = [];
    for (const m of all) {
      const role =
        m.role === 'user'
          ? 'User'
          : m.role === 'assistant'
            ? 'AI Assistant'
            : m.role || 'Message';
      const text = getMessageText(m).replace(/\r?\n/g, ' ');
      lines.push(`${role}: ${text}`);
    }
    const blob = new Blob([lines.join('\n')], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `chat-${id}.txt`;
    document.body.appendChild(a);
    a.click();
    a.remove();
    URL.revokeObjectURL(url);
  };

  // Keep a ref copy of messages so the onToolCall handler (created before
  // useChat returns) can inspect the live message stream without referencing
  // the `messages` const before it's declared.
  const messagesRef = useRef<Array<UIMessage>>([]);
  // Track when we last showed a prompt to avoid accidental duplicate UI spam
  const lastShownPromptTs = useRef<Map<string, number>>(new Map());

  const { messages, input, status, handleInputChange, handleSubmit } = useChat({
    id,
    initialMessages,
    generateId: generateUUID,
    api: `${process.env.NEXT_PUBLIC_API_URL}/ai/chat`,
    maxSteps: 5,
    onToolCall: async ({ toolCall }) => {
      const { toolName, args } = toolCall;

      const { searchTerm, id, postcode, type } = args as SpecialistToolArgs;

      console.log('Tool called:', toolCall);
      console.log(toolName);
      console.log(args);

      if (toolName === 'getspecialist') {
        console.log('getspecialist creceived - calling handle search');
        handleSearch({ searchTerm, id, postcode, type });
      }

      const getRequestIdFromArgs = (a: unknown): string | null => {
        if (typeof a === 'object' && a !== null) {
          const rec = a as Record<string, unknown>;
          const v = rec['requestId'] ?? rec['requestID'] ?? rec['request_id'];
          return typeof v === 'string' && v.length > 0 ? v : null;
        }
        return null;
      };

      // Helper: show quick replies but avoid duplicates when the assistant already asked the same thing
      const showQuickRepliesSafely = (
        prompt: string,
        options: Array<{
          label: string;
          value: string;
          requiresInput?: boolean;
        }>,
        requestId?: string | null
      ) => {
        const promptKey = prompt.trim().toLowerCase();

        // If we showed this prompt very recently, skip to avoid UI spam.
        const now = Date.now();
        const lastTs = lastShownPromptTs.current.get(promptKey) ?? 0;
        const DEDUPE_MS = 10_000; // 10s
        if (now - lastTs < DEDUPE_MS) return;

        // If there's a pending assistant message matching the prompt, merge/skip
        if (
          pendingAssistantMessage &&
          getMessageText(pendingAssistantMessage)
            .toLowerCase()
            .includes(promptKey)
        )
          return;

        // If quick replies are already shown, merge options (unique by value)
        if (quickReplies) {
          const merged = [...quickReplies.options, ...options];
          const uniqMap = new Map<
            string,
            { label: string; value: string; requiresInput?: boolean }
          >();
          for (const o of merged) uniqMap.set(o.value, o);
          setQuickReplies({
            id: generateUUID(),
            prompt: quickReplies.prompt || prompt,
            requestId: quickReplies.requestId ?? requestId ?? null,
            options: Array.from(uniqMap.values()),
          });
          lastShownPromptTs.current.set(promptKey, now);
          return;
        }

        setQuickReplies({
          id: generateUUID(),
          prompt,
          requestId: requestId ?? null,
          options,
        });
        lastShownPromptTs.current.set(promptKey, now);
      };

      // Tool-driven collection UI handlers
      if (toolName === 'collect_recipient') {
        // Show quick replies first; setting pendingAssistantMessage before
        // can trigger the duplicate-skip check inside showQuickRepliesSafely.
        showQuickRepliesSafely(
          'Who is this for?',
          [
            { label: 'For myself', value: 'For myself' },
            { label: 'For someone else', value: 'For someone else' },
          ],
          getRequestIdFromArgs(args)
        );
        setPendingAssistantMessage({
          id: generateUUID(),
          role: 'assistant',
          content: 'Who is this for? Please confirm below.',
          parts: [
            { type: 'text', text: 'Who is this for? Please confirm below.' },
          ],
        });
        return;
      }

      if (toolName === 'collect_age_range') {
        // Show quick replies before setting pendingAssistantMessage to avoid
        // the duplicate-skip early-return inside showQuickRepliesSafely.
        showQuickRepliesSafely(
          'Age or age range',
          [
            { label: '<16', value: 'under 16' },
            { label: '16–24', value: '16-24' },
            { label: '25–34', value: '25-34' },
            { label: '35–44', value: '35-44' },
            { label: '45–54', value: '45-54' },
            { label: '55–64', value: '55-64' },
            { label: '65+', value: '65+' },
            {
              label: 'Enter exact age',
              value: 'enter_age',
              requiresInput: true,
            },
          ],
          getRequestIdFromArgs(args)
        );
        setPendingAssistantMessage({
          id: generateUUID(),
          role: 'assistant',
          content: 'Please select an age or age range.',
          parts: [{ type: 'text', text: 'Please select an age or age range.' }],
        });
        return;
      }

      if (toolName !== 'getspecialist') return;

      try {
        const specialistArgs = args as SpecialistToolArgs;

        // UX-level postcode validation (UK) only
        const ukPostcodeRegex =
          /^(GIR 0AA|[A-PR-UWYZ][A-HK-Y]?[0-9][0-9ABEHMNPRV-Y]? ?[0-9][ABD-HJLNP-UW-Z]{2})$/i;
        if (
          specialistArgs.postcode &&
          !ukPostcodeRegex.test(specialistArgs.postcode.trim())
        ) {
          setPendingAssistantMessage({
            id: generateUUID(),
            role: 'assistant',
            content: 'Please enter a valid UK postcode (e.g. SW1A 1AA).',
            parts: [
              {
                type: 'text',
                text: 'Please enter a valid UK postcode (e.g. SW1A 1AA).',
              },
            ],
          });
          return;
        }

        // Show a pending assistant message while server performs the search.
        // Do NOT attempt to run the search locally or forward the tool call.
        setPendingAssistantMessage({
          id: generateUUID(),
          role: 'assistant',
          content: `Searching for ${specialistArgs.speciality} in your area...`,
          parts: [
            {
              type: 'text',
              text: `Searching for ${specialistArgs.speciality}...`,
            },
          ],
        });
      } catch (error) {
        console.error(
          'Error handling getspecialist tool call on client:',
          error
        );
      }
    },
  });

  // Helper to programmatically send a user message via the SDK helpers.
  const [localConfirmed, setLocalConfirmed] = useState(false);

  // Returns a promise that resolves after a short delay to reduce race conditions
  const sendConfirmationMessage = async (
    text: string,
    requestId?: string | null
  ) => {
    // If this is a confirmation, normalize to a canonical short form the server expects
    const isConfirm = /^\s*(confirm|confirmed|yes|yep|yeah|ok|okay)\b/i.test(
      text
    );
    const sendText = isConfirm ? 'Confirmed' : text;
    const sendTextWithReq = requestId
      ? `${sendText} [req:${requestId}]`
      : sendText;

    // Build synthetic events: one with marker for submission, one clean for user-visible input
    const changeEventWithReq = {
      target: { name: 'content', value: sendTextWithReq },
    } as unknown as React.ChangeEvent<HTMLInputElement>;
    const changeEventClean = {
      target: { name: 'content', value: sendText },
    } as unknown as React.ChangeEvent<HTMLInputElement>;

    // Submit the marked value so backend can correlate, then immediately restore the visible input to the clean text.
    try {
      handleInputChange(changeEventWithReq);
      const submitEvent = {
        preventDefault: () => {},
      } as unknown as React.FormEvent<HTMLFormElement>;
      handleSubmit(submitEvent);
    } finally {
      // Restore visible input without the marker (hides marker from user)
      // Use a short timeout to allow SDK to pick up the submitted value synchronously.
      setTimeout(() => handleInputChange(changeEventClean), 0);
    }

    if (isConfirm) setLocalConfirmed(true);

    // Wait until the new user message (clean text) is reflected in messagesRef so the server will see it
    const expected = sendText;
    const start = Date.now();
    const TIMEOUT = 2000;
    const POLL_MS = 100;
    while (Date.now() - start < TIMEOUT) {
      const found = messagesRef.current.some(
        (m) =>
          m.role === 'user' && getMessageText(m).toString().includes(expected)
      );
      if (found) return;
      // small delay
      await new Promise((r) => setTimeout(r, POLL_MS));
    }
    // fallback short wait if not observed
    await new Promise((r) => setTimeout(r, 200));
  };

  // Simple postcode normalization for UK (trim, uppercase, single space)
  const normalizePostcode = (p: string) =>
    p.trim().toUpperCase().replace(/\s+/g, ' ');

  // reference small helpers to avoid unused-var linting while we wire further UX
  useEffect(() => {
    // no-op: keep reference so linters don't flag unused helpers during iterative edits
    void localConfirmed;
    void normalizePostcode;
  }, [localConfirmed]);

  // keep messagesRef up to date so the onToolCall handler can read the latest
  // messages at invocation time
  useEffect(() => {
    messagesRef.current = messages;
  }, [messages]);

  // Clear the temporary pending assistant message once the server streams a real assistant message.
  useEffect(() => {
    if (!pendingAssistantMessage) return;
    const hasNewAssistantMessage = messages.some(
      (m) => m.role === 'assistant' && m.id !== pendingAssistantMessage.id
    );
    if (hasNewAssistantMessage) setPendingAssistantMessage(null);
  }, [messages, pendingAssistantMessage]);

  if (messages.length > 0 && messages.length < 2) {
    window.dataLayer = window.dataLayer || [];
    window.dataLayer.push({
      event: 'ai_chat_initiated',
    });
  }

  return (
    <Box component={'section'} sx={{ marginTop: '2rem' }}>
      <Stack spacing={2}>
        <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
          <Button
            size="small"
            variant="outlined"
            onClick={downloadConversation}
          >
            Download conversation
          </Button>
        </Box>
        {/* Messages first; quick-reply buttons render below messages to encourage the user to follow assistant flow */}
        <Messages
          chatId={id}
          status={status}
          messages={
            pendingAssistantMessage
              ? [...messages, pendingAssistantMessage]
              : messages
          }
        />

        {quickReplies && (
          <Box
            sx={{
              border: '1px dashed',
              borderColor: 'divider',
              p: 1,
              borderRadius: 1,
            }}
          >
            <Box sx={{ mb: 1 }}>{quickReplies.prompt}</Box>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              {quickReplies.options.map((opt) => (
                <Button
                  key={opt.value}
                  size="small"
                  variant="outlined"
                  onClick={async () => {
                    try {
                      const looksLikeConfirm =
                        /confirm/i.test(opt.label) ||
                        /confirm/i.test(opt.value);
                      await sendConfirmationMessage(
                        looksLikeConfirm ? 'Confirmed' : opt.value,
                        quickReplies.requestId ?? null
                      );
                    } finally {
                      setQuickReplies(null);
                      setPendingAssistantMessage(null);
                    }
                  }}
                >
                  {opt.label}
                </Button>
              ))}
            </Box>
          </Box>
        )}
        {quickReplies ? (
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            <Typography variant="body2" color="textSecondary">
              You can choose one of the quick replies or type your own response
              — quick replies are suggestions.
            </Typography>
            <TextField
              fullWidth
              label="Write your query:"
              type="text"
              name="content"
              value={input}
              onChange={handleInputChange}
              helperText="You can also type a custom reply or select an option"
            />
          </Box>
        ) : (
          <form
            onSubmit={(e) => {
              if (pendingAssistantMessage) setPendingAssistantMessage(null);
              handleSubmit(e);
            }}
          >
            <TextField
              fullWidth
              label="Write your query:"
              type="text"
              name="content"
              value={input}
              onChange={handleInputChange}
            />
          </form>
        )}
      </Stack>
    </Box>
  );
}
