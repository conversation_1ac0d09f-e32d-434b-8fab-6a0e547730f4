import { <PERSON>alog, DialogTitle, DialogContent, DialogActions, Button, TextField } from '@mui/material';
import { useState, useEffect } from 'react';

export default function AgePromptModal({ open, initialValue, onClose }: {
  open: boolean;
  initialValue?: string;
  onClose: (value: string | null) => void;
}) {
  const [value, setValue] = useState(initialValue || '');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    setValue(initialValue || '');
    setError(null);
  }, [initialValue, open]);

  const validate = (v: string) => {
    const trimmed = v.trim();
    if (!trimmed) return 'Please enter an age or range.';
    // Accept single integer or range like 45-54
    if (/^\d{1,3}$/.test(trimmed)) {
      const n = parseInt(trimmed, 10);
      if (n < 0 || n > 120) return 'Please enter a realistic age between 0 and 120.';
      return null;
    }
    if (/^\d{1,3}\s*[-–]\s*\d{1,3}$/.test(trimmed)) {
      const parts = trimmed.split(/[-–]/).map(p => parseInt(p.trim(), 10));
      if (parts[0] > parts[1]) return 'Invalid range (low must be <= high).';
      if (parts[0] < 0 || parts[1] > 120) return 'Please provide a realistic age range between 0 and 120.';
      return null;
    }
    return 'Enter an age like "28" or a range like "45-54".';
  };

  const handleOk = () => {
    const v = value.trim();
    const err = validate(v);
    if (err) return setError(err);
    onClose(v);
  };

  return (
    <Dialog open={open} onClose={() => onClose(null)}>
      <DialogTitle>Enter exact age</DialogTitle>
      <DialogContent>
        <TextField
          autoFocus
          fullWidth
          label="Age or range"
          value={value}
          onChange={e => setValue(e.target.value)}
          helperText={error || 'e.g. 28 or 45-54'}
          error={!!error}
        />
      </DialogContent>
      <DialogActions>
        <Button onClick={() => onClose(null)}>Cancel</Button>
        <Button onClick={handleOk} variant="contained">OK</Button>
      </DialogActions>
    </Dialog>
  );
}
