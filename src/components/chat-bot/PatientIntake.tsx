import React, { useState, useMemo } from 'react';
import { Box, Button, TextField, ToggleButton, ToggleButtonGroup, Stack, FormHelperText } from '@mui/material';

export default function PatientIntake({ onConfirm }: { onConfirm: (text: string) => void }) {
  const [who, setWho] = useState<'myself' | 'someone' | null>(null);
  const [ageRange, setAgeRange] = useState<string | null>(null);
  const [ageExact, setAgeExact] = useState<string>('');
  const [touched, setTouched] = useState<{ who?: boolean; age?: boolean }>({});

  const ranges = ['<16', '16–24', '25–34', '35–44', '45–54', '55–64', '65+'];

  const ageExactNumber = useMemo(() => {
    const v = ageExact.trim();
    if (v === '') return null;
    const n = Number(v);
    if (!Number.isFinite(n)) return NaN;
    return n;
  }, [ageExact]);

  const whoError = touched.who && !who ? 'Please select whether this is for yourself or someone else.' : '';

  let ageError = '';
  if (touched.age) {
    if (!ageExact && !ageRange) {
      ageError = 'Please select an age range or enter an exact age.';
    } else if (ageExact) {
      if (Number.isNaN(ageExactNumber as number)) ageError = 'Please enter a valid number for age.';
      else if ((ageExactNumber as number) < 0 || (ageExactNumber as number) > 120) ageError = 'Please enter an age between 0 and 120.';
    }
  }

  const isValid = !!who && (!!ageRange || (ageExact !== '' && !ageError));

  const handleConfirm = () => {
    setTouched({ who: true, age: true });
    if (!isValid) return;
    let ageText = ageExact.trim();
    if (!ageText && ageRange) ageText = ageRange as string;
    const whoText = who === 'myself' ? 'For myself' : 'For someone else';
    const message = ageText ? `${whoText}, age ${ageText}` : `${whoText}`;
    onConfirm(message);
  };

  return (
    <Box sx={{ border: '1px solid', borderColor: 'divider', p: 2, borderRadius: 1 }}>
      <Stack spacing={2}>
        <div>Is this for yourself or someone else?</div>
        <ToggleButtonGroup
          value={who}
          exclusive
          onChange={(_, v) => {
            setWho(v);
            setTouched(t => ({ ...t, who: true }));
          }}
          aria-label="who"
        >
          <ToggleButton value="myself">For myself</ToggleButton>
          <ToggleButton value="someone">For someone else</ToggleButton>
        </ToggleButtonGroup>
        {whoError && <FormHelperText error>{whoError}</FormHelperText>}

        <div>Age or age range</div>
        <ToggleButtonGroup
          value={ageRange}
          exclusive
          onChange={(_, v) => {
            setAgeRange(v);
            setTouched(t => ({ ...t, age: true }));
            // If user picks a range, clear exact age
            if (v) setAgeExact('');
          }}
          aria-label="age-range"
        >
          {ranges.map(r => (
            <ToggleButton key={r} value={r}>{r}</ToggleButton>
          ))}
        </ToggleButtonGroup>

        <TextField
          label="Exact age (optional)"
          value={ageExact}
          onChange={e => {
            setAgeExact(e.target.value);
            setTouched(t => ({ ...t, age: true }));
            // clear ageRange when user types exact age
            if (ageRange) setAgeRange(null);
          }}
          error={!!ageError}
          helperText={ageError}
        />

        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button variant="contained" onClick={handleConfirm} disabled={!isValid}>Confirm</Button>
        </Box>
      </Stack>
    </Box>
  );
}
