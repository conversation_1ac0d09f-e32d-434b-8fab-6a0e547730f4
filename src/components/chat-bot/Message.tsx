import { memo } from 'react';
import type { UIMessage } from 'ai';
import equal from 'fast-deep-equal';
import { Box, Paper, Typography, useTheme } from '@mui/material';
import FadeInAnimation from '../../shared/components/FadeInAnimation';
import { SparklesIcon } from '../icons';
import { AutoAwesome } from '@mui/icons-material';

const PurePreviewMessage = ({ message }: { chatId: string; message: UIMessage }) => {
  const theme = useTheme();
  return (
    <FadeInAnimation variant='reveal-blur-static' delay={0.1}>
      <Box sx={{ display: 'flex', justifyContent: message.role === 'assistant' ? 'flex-start' : 'flex-end' }}>
        <Paper elevation={3} sx={{ display: 'inline-flex', padding: '1rem' }}>
          {message.role === 'assistant' && <AutoAwesome sx={{ fontSize: 24, marginRight: theme.spacing(2) }} />}
          {message.parts?.map((part, partIndex) => {
            const { type } = part;
            const key = `message-${message.id}-part-${partIndex}`;

            if (type === 'text') {
              return (
                <Typography variant='body1' key={key} sx={{ textAlign: 'justify' }}>
                  {part.text}
                </Typography>
              );
            }
            if (type === 'tool-invocation') {
              return (
                <Typography variant='body1' key={key} sx={{ textAlign: 'justify' }}>
                  {part.toolInvocation.args.message}
                </Typography>
              );
            }
          })}
        </Paper>
      </Box>
    </FadeInAnimation>
  );
};

export const PreviewMessage = memo(PurePreviewMessage, (prevProps, nextProps) => {
  if (prevProps.message.id !== nextProps.message.id) return false;

  if (!equal(prevProps.message.parts, nextProps.message.parts)) return false;

  return true;
});

export const ThinkingMessage = () => {
  const role = 'assistant';

  return (
    <FadeInAnimation variant='reveal-blur-static' delay={0.1} data-role={role}>
      <Box>
        <SparklesIcon size={14} />
      </Box>
      <Typography>Hmm...</Typography>
    </FadeInAnimation>
  );
};
