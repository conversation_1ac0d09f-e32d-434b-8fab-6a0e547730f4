'use client';

import { Box, Typography } from '@mui/material';
import React from 'react';
import FadeInAnimation from '../../shared/components/FadeInAnimation';
import { Chat } from './Chat';
import { generateUUID } from '../../lib/utils';

// TODOS
// I am drilling handle search through this component, is it acceptable in this case or should I do something else
export function AIChat({
  handleSearch,
}: {
  // TODO - fix any issue

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  handleSearch: (params: any) => void;
}) {
  const id = generateUUID();

  return (
    <Box textAlign="center" my={4}>
      <FadeInAnimation variant="reveal-blur-static" delay={0.1}>
        <Typography variant="h5" fontWeight="bold" textAlign="center">
          AI Assistant Activated
        </Typography>
      </FadeInAnimation>
      <FadeInAnimation variant="reveal-blur-static" delay={0.2}>
        <Typography
          variant="body1"
          color="text.secondary"
          mt={2}
          textAlign="center"
        >
          Let our AI tool guide you to the best procedure for your needs.
        </Typography>
      </FadeInAnimation>
      <FadeInAnimation variant="reveal-blur-static" delay={0.3}>
        <Chat
          // handleSearch={handleSearch}
          handleSearch={(params) =>
            handleSearch({ ...params, searchSource: 'ai' })
          }
          key={id}
          id={id}
          initialMessages={[]}
        />
      </FadeInAnimation>
    </Box>
  );
}
