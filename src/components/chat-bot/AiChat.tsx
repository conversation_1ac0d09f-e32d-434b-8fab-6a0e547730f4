'use client';

import { Box, Typography } from '@mui/material';
import React from 'react';
import FadeInAnimation from '../../shared/components/FadeInAnimation';
import { Chat } from './Chat';
import { generateUUID } from '../../lib/utils';
import { SearchTerm } from '@/lib/api/types/search-term.type';

// TODOS
// I am drilling handle search through this component, is it acceptable in this case or should I do something else
export function AIChat({
  handleSearch,
}: {
  handleSearch: ({
    searchTerm,
    postcode,
    distance,
  }: {
    searchTerm: SearchTerm;
    postcode: string | undefined;
    distance: number | undefined;
  }) => void;
}) {
  const id = generateUUID();

  return (
    <Box textAlign="center" my={4}>
      <FadeInAnimation variant="reveal-blur-static" delay={0.1}>
        <Typography variant="h5" fontWeight="bold" textAlign="center">
          AI Assistant Activated
        </Typography>
      </FadeInAnimation>
      <FadeInAnimation variant="reveal-blur-static" delay={0.2}>
        <Typography
          variant="body1"
          color="text.secondary"
          mt={2}
          textAlign="center"
        >
          Let our AI tool guide you to the best procedure for your needs.
        </Typography>
      </FadeInAnimation>
      <FadeInAnimation variant="reveal-blur-static" delay={0.3}>
        <Chat
          handleSearch={handleSearch}
          key={id}
          id={id}
          initialMessages={[]}
        />
      </FadeInAnimation>
    </Box>
  );
}
