import { UseChatHelpers } from '@ai-sdk/react';
import { UIMessage } from 'ai';
import { Greeting } from './Greeting';
import { PreviewMessage, ThinkingMessage } from './Message';
import { Stack } from '@mui/material';
import { memo } from 'react';
import equal from 'fast-deep-equal';

interface MessagesProps {
  chatId: string;
  status: UseChatHelpers['status'];
  messages: Array<UIMessage>;
}
function PureMessages({ chatId, status, messages }: MessagesProps) {
  // const { hasSentMessage } = useMessages({ chatId, status });
  return (
    <Stack spacing={2}>
      {messages.length === 0 && <Greeting />}
      {messages.map((message) => (
        <PreviewMessage key={message.id} chatId={chatId} message={message} />
      ))}
      {status === 'submitted' && messages.length > 0 && messages[messages.length - 1].role === 'user' && (
        <ThinkingMessage />
      )}
    </Stack>
  );
}

export const Messages = memo(PureMessages, (prevProps, nextProps) => {
  if (prevProps.status !== nextProps.status) return false;
  if (prevProps.status && nextProps.status) return false;
  if (prevProps.messages.length !== nextProps.messages.length) return false;
  if (!equal(prevProps.messages, nextProps.messages)) return false;

  return true;
});
