import { UseChatHelpers } from '@ai-sdk/react';
import { UIMessage } from 'ai';
import { Greeting } from './Greeting';
import { PreviewMessage, ThinkingMessage } from './Message';
import { Stack } from '@mui/material';
import { memo, useEffect, useRef } from 'react';
import equal from 'fast-deep-equal';
import { IsMobile } from '@/lib/utils';

interface MessagesProps {
  chatId: string;
  status: UseChatHelpers['status'];
  messages: Array<UIMessage>;
}

function PureMessages({ chatId, status, messages }: MessagesProps) {
  const messagesEndRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (messagesEndRef.current) {
      try {
        // console.log('messages end into view attempted');
        // First, try to scroll the messages end into view.
        messagesEndRef.current.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
        });
        if (IsMobile()) {
          setTimeout(() => {
            window.scrollBy({
              top: -100, // Adjust this value - negative scrolls up
              behavior: 'smooth',
            });
          }, 100); // Small delay to let initial scroll start
        }
        // Then force the browser to scroll to the absolute bottom of the page
        // after the current frame to ensure elements rendered after Messages
        // (like the input) are visible.
        // requestAnimationFrame(() => {
        //   try {
        //     console.log('absolute bottom of the page attempted');
        //     window.scrollTo({
        //       top: document.body.scrollHeight,
        //       behavior: 'smooth',
        //     });
        //   } catch {
        //     // ignore
        //   }
        // });

        // Extra fallback in case rendering/layout takes longer (mobile/slow):
        // setTimeout(() => {
        //   try {
        //     console.log(
        //       'Extra fallback in case rendering/layout takes longer attempted'
        //     );

        //     window.scrollTo({
        //       top: document.body.scrollHeight,
        //       behavior: 'smooth',
        //     });
        //   } catch {
        //     // ignore
        //   }
        // }, 120);

        return;
      } catch {
        // Fallback to window scroll if scrollIntoView fails for any reason
      }
    }

    // Final fallback: scroll to bottom of the page
    // console.log('Final fallback: scroll to bottom of the page attempted');

    // window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
  }, [messages]);

  return (
    <Stack spacing={2}>
      {messages.length === 0 && <Greeting />}
      {messages.map((message) => (
        <PreviewMessage key={message.id} chatId={chatId} message={message} />
      ))}
      {status === 'submitted' &&
        messages.length > 0 &&
        messages[messages.length - 1].role === 'user' && <ThinkingMessage />}
      <div ref={messagesEndRef} />
    </Stack>
  );
}

export const Messages = memo(PureMessages, (prevProps, nextProps) => {
  if (prevProps.status !== nextProps.status) return false;
  if (prevProps.status && nextProps.status) return false;
  if (prevProps.messages.length !== nextProps.messages.length) return false;
  if (!equal(prevProps.messages, nextProps.messages)) return false;

  return true;
});
