'use client';

import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Chip,
  IconButton,
  useTheme,
  Link,
  Grid,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import PersonIcon from '@mui/icons-material/Person';
import PhoneIcon from '@mui/icons-material/Phone';
import LanguageIcon from '@mui/icons-material/Language';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import { Clinic } from '../../types/clinic.type';
import Image from 'next/image';
// import { useMemo } from 'react';
// import { dummyImages } from '../../../public/images/dummyClinicImages/index';
import { IsMobile } from '../../lib/utils';
import { trackProviderClick } from '@/utils/tracking';
// import { trackLeadGeneration } from '@/utils/tracking';

interface ClinicDetailModalProps {
  result: Clinic;
  open: boolean;
  index: number;
  onClose: () => void;
}

type LeadActionType = 'website' | 'phone';

const ClinicDetailModal = ({
  index,
  result,
  open,
  onClose,
}: ClinicDetailModalProps) => {
  const theme = useTheme();
  // const IsMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Convert distance from meters to miles (if available)
  const distanceInMiles = result.distance
    ? Math.round((result.distance / 1609.34) * 10) / 10
    : null;

  const dummyDescription = `${result.name} is the foremost clinic in London for your ailment, trust us, book with them and all your woes will be cured instantly.`;

  // Use full description without truncation
  const displayDescription = result.description || dummyDescription;

  const handleLeadGenerationClick = (
    result: Clinic,
    index: number,
    actionType: LeadActionType
  ) => {
    trackProviderClick({
      clinicId: result.id,
      clinicName: result.name,
      index: index + 1,
      actionType: actionType, // 'website' or 'phone'
      websiteUrl: result.linkToWebsite || null,
      phoneNumber: result.telephoneNumber || null,
      rating: result.rating || null,
    });
    // });
  };

  return (
    <Modal
      open={open}
      onClose={onClose}
      aria-labelledby="clinic-detail-modal"
      aria-describedby="detailed-information-about-clinic"
    >
      <Box
        sx={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: IsMobile() ? '95%' : '85%',
          maxWidth: '1000px', // Increased from 900px
          height: IsMobile() ? '90vh' : '95vh', // Increased to 90vh on desktop
          bgcolor: 'background.paper',
          borderRadius: '16px',
          boxShadow: 24,
          overflow: 'hidden', // Changed from 'auto' to 'hidden'
          p: 0,
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        {/* Header with image */}
        <Box sx={{ position: 'relative', height: IsMobile() ? 180 : 250 }}>
          <Image
            src={
              result.logo ||
              'https://selfpayhealth.s3.eu-west-2.amazonaws.com/logo.png'
            }
            alt={`${result.name} header image`}
            fill
            style={{ objectFit: 'contain' }}
          />

          {/* Overlay with clinic name for better readability
          <Box
            sx={{
              position: 'absolute',
              bottom: 0,
              left: 0,
              right: 0,
              background: 'linear-gradient(transparent, rgba(0,0,0,0.7))',
              p: 2,
            }}
          >
            <Typography variant="h5" color="white" fontWeight="bold">
              {clinic.name}
            </Typography>
          </Box> */}

          {/* Close button */}
          <IconButton
            onClick={onClose}
            sx={{
              position: 'absolute',
              top: 16,
              right: 16,
              bgcolor: 'rgba(0, 0, 0, 0.5)',
              color: 'white',
              '&:hover': {
                bgcolor: 'rgba(0, 0, 0, 0.7)',
              },
            }}
          >
            <CloseIcon />
          </IconButton>
        </Box>

        {/* Content */}
        <Box
          sx={{
            p: { xs: 3, md: 4 },
            flex: 1,
            overflowY: 'auto',
          }}
        >
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              mb: 2,
            }}
          >
            <Typography variant="h4" fontWeight="bold">
              {result.name}
            </Typography>

            {distanceInMiles && (
              <Chip
                icon={<LocationOnIcon />}
                label={`${distanceInMiles} miles away`}
                sx={{
                  backgroundColor: theme.palette.primary.light,
                  color: theme.palette.primary.contrastText,
                  fontWeight: 'bold',
                  fontSize: '0.9rem',
                  py: 0.5,
                }}
              />
            )}
          </Box>

          {/* Description */}
          <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
            {displayDescription}
          </Typography>

          <Grid container spacing={4}>
            {/* Contact information */}
            <Grid size={{ xs: 12, md: 6 }}>
              <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
                Contact Information
              </Typography>

              {result.telephoneNumber && (
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <PhoneIcon
                    sx={{ mr: 1, color: theme.palette.primary.main }}
                  />
                  <Typography>
                    <Link
                      href={`tel:${result.telephoneNumber}`}
                      color="inherit"
                    >
                      {result.telephoneNumber}
                    </Link>
                  </Typography>
                </Box>
              )}

              {result.linkToWebsite && (
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <LanguageIcon
                    sx={{ mr: 1, color: theme.palette.primary.main }}
                  />
                  <Typography>
                    <Link
                      href={result.linkToWebsite as string}
                      target="_blank"
                      rel="noopener noreferrer"
                      color="inherit"
                    >
                      Visit Website
                    </Link>
                  </Typography>
                </Box>
              )}
            </Grid>

            {/* Consultants - with max height and scrolling */}
            <Grid size={{ xs: 12, md: 6 }}>
              {result.consultants && result.consultants.length > 0 && (
                <>
                  <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
                    Consultants
                  </Typography>
                  <Box
                    display="flex"
                    flexWrap="wrap"
                    gap={1}
                    // sx={{
                    //   maxHeight: '150px', // Fixed height for scrolling
                    //   overflow: 'auto',
                    //   pr: 1,
                    // }}
                  >
                    {result.consultants.map((consultant, index) => {
                      // Create full name with title, firstName and lastName
                      const fullName = [
                        consultant.title,
                        consultant.firstName,
                        consultant.lastName,
                      ]
                        .filter(Boolean)
                        .join(' ');

                      return (
                        <Chip
                          key={index}
                          icon={<PersonIcon fontSize="small" />}
                          label={fullName || consultant.toString()}
                          size="small"
                          sx={{
                            backgroundColor: theme.palette.grey[100],
                            '&:hover': {
                              backgroundColor: theme.palette.grey[200],
                            },
                          }}
                        />
                      );
                    })}
                  </Box>
                </>
              )}
            </Grid>
          </Grid>
        </Box>

        {/* Action buttons */}
        <Box
          sx={{
            mt: 1,
            p: 2,
            borderTop: `1px solid ${theme.palette.grey[200]}`,
          }}
        >
          <Box
            sx={{
              display: 'flex',
              flexDirection: IsMobile() ? 'column' : 'row',
              gap: 2,
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Button
              variant="contained"
              fullWidth={IsMobile()}
              sx={{
                background: `linear-gradient(135deg, ${theme.palette.secondary.main}, ${theme.palette.secondary.light})`,
                color: '#fff',
                fontWeight: 'bold',
                px: 4,
                py: 1.5,
                borderRadius: '8px',
                '&:hover': {
                  background: `linear-gradient(135deg, ${theme.palette.secondary.main}, ${theme.palette.secondary.dark})`,
                },
              }}
            >
              Book Today - coming in 2025
            </Button>

            {result.linkToWebsite && (
              <Button
                // onClick={() => handleLeadGenerationClick(result, index, 'website')}
                onClick={() =>
                  handleLeadGenerationClick(result, index, 'website')
                }
                target="_blank"
                rel="noopener noreferrer"
                variant="outlined"
                fullWidth={IsMobile()}
                href={result.linkToWebsite as string}
                sx={{
                  borderColor: theme.palette.primary.main,
                  color: theme.palette.primary.main,
                  px: 4,
                  py: 1.5,
                  borderRadius: '8px',
                  '&:hover': {
                    borderColor: theme.palette.primary.dark,
                    backgroundColor: theme.palette.primary.light,
                  },
                }}
              >
                <LanguageIcon
                  sx={{ mr: 1, color: theme.palette.primary.main }}
                />
                Visit website
              </Button>
            )}

            {result.telephoneNumber && (
              <Button
                onClick={() =>
                  handleLeadGenerationClick(result, index, 'phone')
                }
                variant="outlined"
                fullWidth={IsMobile()}
                startIcon={<PhoneIcon />}
                href={`tel:${result.telephoneNumber}`}
                sx={{
                  borderColor: theme.palette.primary.main,
                  color: theme.palette.primary.main,
                  px: 4,
                  py: 1.5,
                  borderRadius: '8px',
                  '&:hover': {
                    borderColor: theme.palette.primary.dark,
                    backgroundColor: theme.palette.primary.light,
                  },
                }}
              >
                Call Clinic
              </Button>
            )}
          </Box>
        </Box>
      </Box>
    </Modal>
  );
};

export default ClinicDetailModal;
