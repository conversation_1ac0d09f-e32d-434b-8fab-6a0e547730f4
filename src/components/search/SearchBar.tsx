'use client';

import React, { useEffect, useState } from 'react';
import {
  Box,
  TextField,
  Typography,
  IconButton,
  Slider,
  Autocomplete,
  InputAdornment,
  AutocompleteRenderInputParams,
  CircularProgress,
  Grid,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import StraightenIcon from '@mui/icons-material/Straighten';
import { useTheme } from '@mui/material/styles';
import FadeInAnimation from '../../shared/components/FadeInAnimation';
import { SearchTerm } from '../../lib/api/types/search-term.type';
import { getClinicsSearchTerms } from '../../lib/api/clinics';
import { IsMobile } from '../../lib/utils';

// UK postcode validation regex
const validateUKPostcode = (postcode: string): boolean => {
  // This regex covers standard UK postcode formats
  const regex = /^[A-Z]{1,2}[0-9][A-Z0-9]? ?[0-9][A-Z]{2}$/i;
  return postcode.trim() === '' || regex.test(postcode.trim());
};

const SearchBar = ({
  handleSearch,
}: {
  handleSearch: ({
    searchTerm,
    postcode,
    distance,
  }: {
    searchTerm: SearchTerm;
    postcode: string | undefined;
    distance: number | undefined;
  }) => void;
}) => {
  const theme = useTheme();
  const [state, setState] = useState<{
    searchTerm: SearchTerm | null;
    postcode: string | undefined;
    distance: number | undefined;
  }>({
    searchTerm: null,
    postcode: undefined,
    distance: undefined,
  });
  const [searchTerms, setSearchTerms] = useState<SearchTerm[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [postcodeError, setPostcodeError] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState(false);

  const updateSearchTerm = (newSearchTerm: SearchTerm | null) => {
    setState((prevState) => ({ ...prevState, searchTerm: newSearchTerm }));
  };

  const updatePostcode = (newPostcode: string) => {
    const isValid = validateUKPostcode(newPostcode);
    setPostcodeError(!isValid);
    setState((prevState) => ({ ...prevState, postcode: newPostcode }));
  };

  const updateDistance = (newDistance: number) => {
    setState((prevState) => ({
      ...prevState,
      distance: newDistance < 1 ? undefined : newDistance,
    }));
  };

  const getSearchTerms = async () => {
    setIsLoading(true);
    const terms = await getClinicsSearchTerms();
    setSearchTerms(terms);
    setIsLoading(false);
  };

  useEffect(() => {
    getSearchTerms();
  }, []);

  const { searchTerm, postcode, distance } = state;

  return (
    <FadeInAnimation
      variant="pop-in"
      delay={0.1}
      alignItems="center"
      sx={{
        background: 'rgba(255, 255, 255, 0.85)',
        backdropFilter: 'blur(18px)',
        borderRadius: 2,
        padding: IsMobile() ? '20px 25px' : '22px 30px',
        boxShadow: '0 14px 34px rgba(0, 0, 0, 0.07)',
        border: `1px solid ${theme.palette.grey[200]}`,
        maxWidth: '1220px',
        width: '100%',
        transition: 'all 0.3s ease',
      }}
    >
      {error && (
        <Typography variant="body1" color="error" sx={{ mb: 1, ml: 5 }}>
          {error}
        </Typography>
      )}

      <Grid container spacing={2}>
        <Grid size={{ xs: 12, sm: 7 }}>
          <Autocomplete
            fullWidth
            options={searchTerms}
            disabled={isLoading}
            isOptionEqualToValue={(option: SearchTerm, value: SearchTerm) =>
              option.name === value.name
            }
            getOptionLabel={(option) =>
              typeof option === 'string' ? option : option.name
            }
            onChange={(event, value) => {
              setError(null);
              updateSearchTerm(value);
              if (value && value.name) {
                window.dataLayer = window.dataLayer || [];
                window.dataLayer.push({
                  event: 'search_term_selected',
                  search_term: value.name,
                  search_term_id: value.id, // Optional: include ID too
                  search_term_type: value.type, // Optional: include ID too
                });
              }
            }}
            renderOption={(props, option) => (
              <li {...props} key={option.id}>
                {option.name}
              </li>
            )}
            renderInput={(params: AutocompleteRenderInputParams) => (
              <TextField
                {...params}
                slotProps={{
                  input: {
                    ...params.InputProps,
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon
                          sx={{
                            color: theme.palette.primary.main,
                            mr: 1.5,
                            display: IsMobile() ? 'none' : 'block',
                          }}
                        />
                      </InputAdornment>
                    ),
                    endAdornment: isLoading ? (
                      <InputAdornment position="end">
                        <CircularProgress
                          size="1.5rem"
                          sx={{
                            color: '#ccc',
                          }}
                        />
                      </InputAdornment>
                    ) : null,
                  },
                }}
                label="Search procedures or specialists..."
              />
            )}
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 2 }}>
          <TextField
            fullWidth
            label="Postcode"
            value={postcode || ''}
            onChange={
              (event) => updatePostcode(event.target.value) /* TODO Debounce */
            }
            error={postcodeError}
            helperText={postcodeError ? 'Invalid UK postcode' : ''}
            slotProps={{
              input: {
                startAdornment: (
                  <InputAdornment position="start">
                    <LocationOnIcon
                      sx={{ color: theme.palette.primary.main }}
                    />
                  </InputAdornment>
                ),
              },
            }}
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 2 }} sx={{ px: 1 }}>
          <Box
            display="flex"
            alignItems="center"
            gap={1}
            sx={{
              transform: 'translate(0, -9px) scale(0.75)',
              transformOrigin: 'top left',
              position: 'absolute',
            }}
          >
            <StraightenIcon fontSize="small" color="action" />
            <Typography variant="body1">Distance (miles)</Typography>
          </Box>
          <Slider
            value={distance || 20}
            onChange={(event, val) => {
              updateDistance(val);
            }}
            min={0}
            max={20}
            step={5}
            valueLabelDisplay="auto"
            sx={{
              color: theme.palette.primary.main,
              marginBottom: 0,
              marginTop: '12px',
            }}
            marks={[
              { value: 0, label: '0' },
              { value: 5 },
              { value: 10 },
              { value: 15 },
              { value: 20, label: '20' },
            ]}
          />
        </Grid>
        <Grid
          size={{ xs: 12, sm: 1 }}
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'flex-end',
          }}
        >
          <IconButton
            className="gtm-search-button"
            onClick={() => {
              setError(null);
              if (!searchTerm) {
                setError('Please select a valid option from the list.');
              }
              if (searchTerm && !postcodeError) {
                handleSearch({ searchTerm, postcode, distance });
              }
            }}
            sx={{
              ...{
                // background: `linear-gradient(135deg, ${theme.palette.secondary.main}, ${theme.palette.secondary.light})`,
                background: theme.palette.secondary.light,
                color: '#fff',
                width: 64,
                height: 42,
                borderRadius: 1,
                boxShadow: `0 8px 20px ${theme.palette.secondary.main}33`,
                marginLeft: 0,
                transition: 'all 0.2s ease',
                '&:hover': {
                  background: theme.palette.secondary.dark,
                },
              },
              ...(IsMobile() && {
                width: '100%',
                borderRadius: '5px',
              }),
            }}
          >
            <SearchIcon className="gtm-search-button" sx={{ fontSize: 26 }} />
          </IconButton>
        </Grid>
      </Grid>
    </FadeInAnimation>
  );
};

export default SearchBar;
