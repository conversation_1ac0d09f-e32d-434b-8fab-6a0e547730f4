'use client';

import React from 'react';
import { Typography, Box, useTheme, Grid } from '@mui/material';
import ResultCard from './ResultsCard';
import FadeInAnimation from '../../shared/components/FadeInAnimation';
import { Clinic } from '../../types/clinic.type';
import { IsMobile } from '../../lib/utils';
import { SearchBarParams } from '@/lib/api/types/searchbar-params.type';
// import { SearchTerm } from '@/lib/api/types/search-term.type';

export interface ResultsSectionProps {
  clinics: Clinic[];
  onViewClinicDetails: (clinic: Clinic, index: number) => void;
  searchParams: SearchBarParams;
  searchSource: 'manual' | 'ai';
}

const ResultsSection = ({
  clinics,
  onViewClinicDetails,
  searchParams,
  searchSource,
}: ResultsSectionProps) => {
  const theme = useTheme();

  const { searchTerm, postcode, distance } = searchParams;

  const generateResultsMessage = () => {
    if (!searchTerm || clinics.length < 1) return '';

    let message = `Found ${clinics.length} result${clinics.length !== 1 ? 's' : ''} for "${searchTerm.name}"`;

    if (postcode && distance && distance > 0) {
      message += ` within ${distance} mile${distance !== 1 ? 's' : ''} of ${postcode.toUpperCase()}`;
    } else if (postcode) {
      message += ` near ${postcode.toUpperCase()}`;
    }

    return message;
  };

  return (
    <Box mt={IsMobile() ? 5 : 10}>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          width: '100%',
        }}
      >
        <Box
          sx={{
            flex: 1,
            height: '1px',
            backgroundColor: theme.palette.grey[300],
          }}
        />
        <Box
          sx={{
            width: 50,
            height: 6,
            borderRadius: 4,
            backgroundColor: theme.palette.primary.main,
            mx: 2,
            animation: 'expandFade 1s ease-in-out',
            '@keyframes expandFade': {
              '0%': { width: 5, opacity: 0 },
              '100%': { width: 50, opacity: 1 },
            },
          }}
        />
        <Box
          sx={{
            flex: 1,
            height: '1px',
            backgroundColor: theme.palette.grey[300],
          }}
        />
      </Box>
      <Typography
        variant="h6"
        mt="3rem"
        fontStyle="italic"
        sx={{
          mb: 2,
          color: 'text.secondary',
        }}
      >
        {generateResultsMessage()}
      </Typography>
      <Grid container spacing={3} sx={{ marginTop: 5 }}>
        {clinics.length > 0 ? (
          clinics.map((clinic, index) => (
            <Grid size={{ xs: 12, sm: 6, md: 4 }} key={clinic.id}>
              <FadeInAnimation variant="reveal-blur-static" delay={0.25}>
                <ResultCard
                  searchSource={searchSource}
                  searchParams={searchParams}
                  clinic={clinic}
                  index={index}
                  onViewClinicDetails={onViewClinicDetails}
                />
              </FadeInAnimation>
            </Grid>
          ))
        ) : (
          <Grid size={12}>
            <Typography variant="body1" color="text.secondary">
              {postcode && distance && distance < 20
                ? `We are sorry we didn't find any providers for this search - try increasing the search distance.`
                : `Unfortunately it looks like we don't have providers for what you are looking for yet - we are working on increasing our offering.`}
            </Typography>
          </Grid>
        )}
      </Grid>
    </Box>
  );
};

export default ResultsSection;
