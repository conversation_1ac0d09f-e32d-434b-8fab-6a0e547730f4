'use client';

import React from 'react';
import { Typography, Box, useTheme, Grid } from '@mui/material';
import ResultCard from './ResultsCard';
import FadeInAnimation from '../../shared/components/FadeInAnimation';
import { Clinic } from '../../types/clinic.type';
import { IsMobile } from '../../lib/utils';
// import { SearchTerm } from '@/lib/api/types/search-term.type';

export interface ResultsSectionProps {
  results: Clinic[];
  // searchTerm: SearchTerm;
}

const ResultsSection = ({ results }: ResultsSectionProps) => {
  const theme = useTheme();

  return (
    <Box mt={IsMobile() ? 5 : 10}>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          width: '100%',
        }}
      >
        <Box
          sx={{
            flex: 1,
            height: '1px',
            backgroundColor: theme.palette.grey[300],
          }}
        />
        <Box
          sx={{
            width: 50,
            height: 6,
            borderRadius: 4,
            backgroundColor: theme.palette.primary.main,
            mx: 2,
            animation: 'expandFade 1s ease-in-out',
            '@keyframes expandFade': {
              '0%': { width: 5, opacity: 0 },
              '100%': { width: 50, opacity: 1 },
            },
          }}
        />
        <Box
          sx={{
            flex: 1,
            height: '1px',
            backgroundColor: theme.palette.grey[300],
          }}
        />
      </Box>
      <Grid container spacing={3} sx={{ marginTop: 2 }}>
        {results.length > 0 ? (
          results.map((result, index) => (
            <Grid size={{ xs: 12, sm: 6, md: 4 }} key={result.id}>
              <FadeInAnimation variant="reveal-blur-static" delay={0.25}>
                <ResultCard
                  result={result}
                  index={index}
                  // searchTerm={searchTerm}
                />
              </FadeInAnimation>
            </Grid>
          ))
        ) : (
          <Grid size={12}>
            <Typography variant="body2" color="text.secondary">
              I&apos;m sorry we didn&apos;t find any providers for this search.
            </Typography>
          </Grid>
        )}
      </Grid>
    </Box>
  );
};

export default ResultsSection;
