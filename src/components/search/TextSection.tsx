'use client';

import { Search } from '@mui/icons-material';
import AutoAwesomeIcon from '@mui/icons-material/AutoAwesome';
import {
  Box,
  Typography,
  ToggleButtonGroup,
  ToggleButton,
  useTheme,
} from '@mui/material';
import FadeInAnimation from '../../shared/components/FadeInAnimation';
import { TextSectionProps } from '@/types/search.type';
import { useEffect, useState } from 'react';

const TextSection = ({ aiSelected, setAiSelected }: TextSectionProps) => {
  const theme = useTheme();
  const [wordIndex, setWordIndex] = useState(0);
  const words = ['Procedure', 'Specialist', 'Condition'];

  useEffect(() => {
    const interval = setInterval(() => {
      setWordIndex((prevIndex) => (prevIndex + 1) % words.length);
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  return (
    <Box
      display="flex"
      flexDirection="column"
      alignItems={{ xs: 'center', md: 'flex-start' }}
      textAlign={{ xs: 'center', md: 'left' }}
      gap={3}
      maxWidth={1000}
    >
      <FadeInAnimation variant="pop-spring" delay={0.2}>
        <Typography variant="h2" color="text.primary" fontWeight="bold">
          Find your{' '}
          <Box
            component="span"
            sx={{
              background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.primary.light}, ${theme.palette.primary.main})`,
              backgroundSize: '200% auto',
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              color: 'transparent',
              WebkitTextFillColor: 'transparent',
              animation: 'shine 3s ease-in-out infinite',
              fontWeight: 'inherit',
              display: 'inline-block',
              minWidth: '180px',
              '@keyframes shine': {
                '0%': { backgroundPosition: '0% center' },
                '100%': { backgroundPosition: '200% center' },
              },
            }}
          >
            {words[wordIndex]}
          </Box>
        </Typography>
      </FadeInAnimation>

      <FadeInAnimation variant="fade-in" delay={0.4}>
        <Typography variant="body1" color="text.secondary" maxWidth={600}>
          Getting the medical help you need shouldn’t be confusing. Quickly find
          what you need by using our search bar or let our AI help you navigate
          your choices.
        </Typography>
      </FadeInAnimation>

      <FadeInAnimation variant="float-up" delay={0.6}>
        <ToggleButtonGroup
          value={aiSelected}
          exclusive
          onChange={() => setAiSelected(!aiSelected)}
          sx={{
            backgroundColor: '#e0e0e0',
            borderRadius: '30px',
            padding: '3px',
            boxShadow: '0px 2px 5px rgba(0,0,0,0.2)',
            mt: { xs: 2, md: 1 },
          }}
        >
          <ToggleButton
            disabled={!aiSelected}
            value={false}
            selected={!aiSelected}
            sx={{
              flex: 1,
              padding: '8px 20px',
              borderRadius: '30px',
              color: !aiSelected ? 'white' : '#666',
              backgroundColor: !aiSelected ? 'primary.main' : '#E0E0E0',
              fontWeight: 'bold',
              typography: 'button',
              '&.Mui-selected': {
                backgroundColor: 'primary.main',
                color: 'white',
              },
            }}
          >
            <Search sx={{ mr: 1 }} />
            Search
          </ToggleButton>

          <ToggleButton
            disabled={aiSelected}
            value={true}
            selected={aiSelected}
            sx={{
              flex: 1,
              padding: '8px 20px',
              borderRadius: '30px',
              color: aiSelected ? 'white' : '#666',
              backgroundColor: aiSelected ? 'primary.main' : '#E0E0E0',
              fontWeight: 'bold',
              typography: 'button',
              '&.Mui-selected': {
                backgroundColor: 'primary.main',
                color: 'white',
              },
            }}
          >
            <AutoAwesomeIcon sx={{ mr: 1 }} />
            AI
          </ToggleButton>
        </ToggleButtonGroup>
      </FadeInAnimation>
    </Box>
  );
};

export default TextSection;
