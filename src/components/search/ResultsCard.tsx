'use client';

import {
  Card,
  CardMedia,
  CardContent,
  Typography,
  Button,
  Box,
  useTheme,
  Chip,
} from '@mui/material';
import PersonIcon from '@mui/icons-material/Person';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import { Clinic } from '../../types/clinic.type';
import { useState } from 'react';
// import { dummyImages } from '../../../public/images/dummyClinicImages/index';
// import logoAsPlaceholder from '../../../public/images/logos/logo.png';
import ClinicDetailModal from './ClinicDetailModal';
import { trackViewItem } from '@/utils/tracking';
// import { SearchTerm } from '@/lib/api/types/search-term.type';

interface ResultCardProps {
  result: Clinic;
  index: number;
  // searchTerm: SearchTerm;
}

const ResultCard = ({ result, index }: ResultCardProps) => {
  const theme = useTheme();
  const [detailModalOpen, setDetailModalOpen] = useState(false);

  // Convert distance from meters to miles (if available)
  const distanceInMiles = result.distance
    ? Math.round((result.distance / 1609.34) * 10) / 10
    : null;

  const dummyDescription = `${result.name} is the foremost clinic in London for your aliment, trust us, book with them and all your woes will be cured instantly`;

  // Limit description to 100 characters
  const displayDescription = result.description
    ? result.description.length > 100
      ? `${result.description.substring(0, 100)}...`
      : result.description
    : dummyDescription.length > 100
      ? `${dummyDescription.substring(0, 100)}...`
      : dummyDescription;

  // Limit consultants to 4
  const displayConsultants = result.consultants
    ? result.consultants.slice(0, 4)
    : [];

  const handleClinicCardClick = () => {
    setDetailModalOpen(true);

    window.dataLayer = window.dataLayer || [];

    // Push clinic card click event
    trackViewItem({
      clinicId: result.id,
      clinicName: result.name,
      index: index + 1,
      rating: result.rating || null,
    });
  };

  const handleCloseDetailModal = () => {
    setDetailModalOpen(false);
  };

  return (
    <>
      <Card
        sx={{
          height: { sm: '100%', md: '700px' },
          display: 'flex',
          flexDirection: 'column',
          backgroundColor: '#ffffff',
          color: theme.palette.text.primary,
          border: `1px solid ${theme.palette.grey[200]}`,
          borderRadius: '12px',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08)',
          transition: 'transform 0.2s ease-in-out',
          '&:hover': {
            transform: 'scale(1.015)',
            boxShadow: '0 6px 18px rgba(0, 0, 0, 0.12)',
          },
        }}
      >
        <CardMedia
          component="img"
          sx={{
            padding: '0 5px',
            height: 250,
            objectFit: 'contain',
            objectPosition: 'center',
          }}
          // src={result.logo || undefined}
          image={
            result.logo ||
            'https://selfpayhealth.s3.eu-west-2.amazonaws.com/logo.png'
          }
          alt={`${result.name} image`}
        />
        <CardContent
          sx={{
            flexGrow: 1,
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'flex-start',
            }}
          >
            <Typography variant="h6" fontWeight="bold" gutterBottom>
              {result.name}
            </Typography>

            {distanceInMiles && (
              <Chip
                icon={<LocationOnIcon fontSize="small" />}
                label={`${distanceInMiles} miles`}
                size="small"
                sx={{
                  backgroundColor: theme.palette.primary.light,
                  color: theme.palette.primary.contrastText,
                  fontWeight: 'bold',
                  ml: 1,
                }}
              />
            )}
          </Box>
          <Box display="flex" alignItems="center" mb={1}>
            {/* <LocalHospitalIcon
                fontSize="small"
                sx={{ color: theme.palette.primary.main, mr: 1 }}
              /> */}
          </Box>

          {/* Description section */}
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{
              mb: 3,
              overflow: 'hidden',
              display: '-webkit-box',
              WebkitLineClamp: 3,
              WebkitBoxOrient: 'vertical',
            }}
          >
            {displayDescription}
          </Typography>
          {/* <a
              href={result.linkToWebsite as string}
              target="_blank"
              rel="noopener noreferrer"
            >
              website
            </a> */}

          {/* Consultants section */}
          {displayConsultants.length > 0 && (
            <Box mb={1}>
              <Typography variant="subtitle2" fontWeight="bold" sx={{ mb: 1 }}>
                Consultants
              </Typography>
              <Box display="flex" flexWrap="wrap" gap={1}>
                {displayConsultants.map((consultant, index) => {
                  // Create full name with title, firstName and lastName
                  const fullName = [
                    consultant.title,
                    consultant.firstName,
                    consultant.lastName,
                  ]
                    .filter(Boolean)
                    .join(' ');

                  return (
                    <Chip
                      key={index}
                      icon={<PersonIcon fontSize="small" />}
                      label={fullName || consultant.toString()}
                      size="small"
                      sx={{
                        backgroundColor: theme.palette.grey[100],
                        '&:hover': {
                          backgroundColor: theme.palette.grey[200],
                        },
                      }}
                    />
                  );
                })}
                {/* Show "more" indicator if there are additional consultants */}
                {result.consultants && result.consultants.length > 4 && (
                  <Chip
                    icon={<PersonIcon fontSize="small" />}
                    label={`+${result.consultants.length - 4} more`}
                    size="small"
                    sx={{
                      backgroundColor: theme.palette.primary.light,
                      color: theme.palette.primary.contrastText,
                      fontWeight: 'bold',
                      '&:hover': {
                        backgroundColor: theme.palette.primary.main,
                      },
                    }}
                  />
                )}
              </Box>
            </Box>
          )}
          {/* </Box> */}

          {/* Spacer to push buttons to bottom */}
          <Box sx={{ flexGrow: 1 }} />

          {/* Buttons at the bottom */}
          <Box display="flex" flexDirection="column" gap={1} mt={2}>
            <Button
              variant="contained"
              fullWidth
              sx={{
                background: `linear-gradient(135deg, ${theme.palette.secondary.main}, ${theme.palette.secondary.light})`,
                color: '#fff',
                fontWeight: 'bold',
                border: '1px solid transparent',
                '&:hover': {
                  background: `linear-gradient(135deg, ${theme.palette.secondary.main}, ${theme.palette.secondary.dark})`,
                },
              }}
            >
              Book Today - coming in 2025
            </Button>

            <Button
              variant="outlined"
              fullWidth
              onClick={handleClinicCardClick}
              sx={{
                borderColor: theme.palette.grey[300],
                color: theme.palette.text.secondary,
                '&:hover': {
                  borderColor: theme.palette.grey[400],
                  backgroundColor: theme.palette.grey[100],
                },
              }}
            >
              See More
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Detail Modal */}
      <ClinicDetailModal
        index={index}
        result={result}
        open={detailModalOpen}
        onClose={handleCloseDetailModal}
      />
    </>
  );
};

export default ResultCard;
