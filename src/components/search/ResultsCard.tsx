'use client';

import {
  CardMedia,
  CardContent,
  Typography,
  Button,
  Box,
  useTheme,
  Chip,
} from '@mui/material';
import PersonIcon from '@mui/icons-material/Person';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import { Clinic } from '../../types/clinic.type';
import { trackViewItem } from '@/utils/tracking';
import { SearchBarParams } from '@/lib/api/types/searchbar-params.type';
// import { SearchTerm } from '@/lib/api/types/search-term.type';

interface ResultCardProps {
  searchParams: SearchBarParams;
  searchSource: 'manual' | 'ai';
  clinic: Clinic;
  index: number;
  onViewClinicDetails: (clinic: Clinic, index: number) => void;
}

const ResultCard = ({
  clinic,
  index,
  onViewClinicDetails,
  searchParams,
  searchSource,
}: ResultCardProps) => {
  const theme = useTheme();

  // Convert distance from meters to miles (if available)
  const distanceInMiles = clinic.distance
    ? Math.round((clinic.distance / 1609.34) * 10) / 10
    : null;

  const dummyDescription = `${clinic.name} welcomes you to their clinic where they offer a range of services and treatments.`;

  // Limit description to 100 characters
  const displayDescription = clinic.description
    ? clinic.description.length > 100
      ? `${clinic.description.substring(0, 100)}...`
      : clinic.description
    : dummyDescription.length > 100
      ? `${dummyDescription.substring(0, 100)}...`
      : dummyDescription;

  // Limit consultants to 4
  const displayConsultants = clinic.consultants
    ? clinic.consultants.slice(0, 4)
    : [];

  const handleClinicCardClick = () => {
    onViewClinicDetails(clinic, index);

    window.dataLayer = window.dataLayer || [];

    // Push clinic card click event
    trackViewItem({
      searchTerm: searchParams.searchTerm,
      postcode: searchParams.postcode,
      distance: searchParams.distance,
      searchSource: searchSource,
      clinicId: clinic.id,
      clinicName: clinic.name,
      index: index + 1,
      rating: clinic.rating || null,
    });
  };

  return (
    <>
      <Box
        onClick={handleClinicCardClick}
        sx={{
          cursor: 'pointer',
          height: { sm: '100%', md: '600px' },
          display: 'flex',
          flexDirection: 'column',
          backgroundColor: '#ffffff',
          color: theme.palette.text.primary,
          border: `1px solid ${theme.palette.grey[200]}`,
          borderRadius: '12px',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08)',
          transition: 'transform 0.2s ease-in-out',
          '&:hover': {
            transform: 'scale(1.015)',
            boxShadow: '0 6px 18px rgba(0, 0, 0, 0.12)',
          },
        }}
      >
        <CardMedia
          component="img"
          sx={{
            padding: '0 5px',
            height: 200,
            objectFit: 'contain',
            objectPosition: 'center',
          }}
          image={
            clinic.logo ||
            'https://selfpayhealth.s3.eu-west-2.amazonaws.com/logo.png'
          }
          alt={`${clinic.name} image`}
        />
        <CardContent
          sx={{
            flexGrow: 1,
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'flex-start',
            }}
          >
            <Typography
              variant="h6"
              // component="h6"
              fontWeight="bold"
              gutterBottom
            >
              {clinic.name}
            </Typography>

            {distanceInMiles && (
              <Chip
                icon={<LocationOnIcon fontSize="small" />}
                label={`${distanceInMiles} miles`}
                size="small"
                sx={{
                  backgroundColor: theme.palette.primary.light,
                  color: theme.palette.primary.contrastText,
                  fontWeight: 'bold',
                  ml: 1,
                }}
              />
            )}
          </Box>
          <Box display="flex" alignItems="center" mb={1}>
            {/* <LocalHospitalIcon
                fontSize="small"
                sx={{ color: theme.palette.primary.main, mr: 1 }}
              /> */}
          </Box>

          {/* Description section */}
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{
              lineHeight: 1.4,
              mb: 3,
              overflow: 'hidden',
              display: '-webkit-box',
              WebkitLineClamp: 3,
              WebkitBoxOrient: 'vertical',
            }}
          >
            {displayDescription}
          </Typography>
          {/* <a
              href={result.linkToWebsite as string}
              target="_blank"
              rel="noopener noreferrer"
            >
              website
            </a> */}

          {/* Consultants section */}
          {displayConsultants.length > 0 && (
            <Box mb={1}>
              <Typography variant="subtitle2" fontWeight="bold" sx={{ mb: 1 }}>
                Consultants
              </Typography>
              <Box display="flex" flexWrap="wrap" gap={1}>
                {displayConsultants.map((consultant, index) => {
                  // Create full name with title, firstName and lastName
                  const fullName = [
                    consultant.title,
                    consultant.firstName,
                    consultant.lastName,
                  ]
                    .filter(Boolean)
                    .join(' ');

                  return (
                    <Chip
                      key={index}
                      icon={<PersonIcon fontSize="small" />}
                      label={fullName || consultant.toString()}
                      size="small"
                      sx={{
                        backgroundColor: theme.palette.grey[100],
                        '&:hover': {
                          backgroundColor: theme.palette.grey[200],
                        },
                      }}
                    />
                  );
                })}
                {/* Show "more" indicator if there are additional consultants */}
                {clinic.consultants && clinic.consultants.length > 4 && (
                  <Chip
                    icon={<PersonIcon fontSize="small" />}
                    label={`+${clinic.consultants.length - 4} more`}
                    size="small"
                    sx={{
                      backgroundColor: theme.palette.primary.light,
                      color: theme.palette.primary.contrastText,
                      fontWeight: 'bold',
                      '&:hover': {
                        backgroundColor: theme.palette.primary.main,
                      },
                    }}
                  />
                )}
              </Box>
            </Box>
          )}
          {/* </Box> */}

          {/* Spacer to push buttons to bottom */}
          <Box sx={{ flexGrow: 1 }} />

          {/* Buttons at the bottom */}
          <Box display="flex" flexDirection="column" gap={1} mt={2}>
            {/* <Button
              variant="contained"
              fullWidth
              sx={{
                background: `linear-gradient(135deg, ${theme.palette.secondary.main}, ${theme.palette.secondary.light})`,
                color: '#fff',
                fontWeight: 'bold',
                border: '1px solid transparent',
                '&:hover': {
                  background: `linear-gradient(135deg, ${theme.palette.secondary.main}, ${theme.palette.secondary.dark})`,
                },
              }}
            >
              Book Today - coming in 2025
            </Button> */}

            <Button
              variant="outlined"
              fullWidth
              onClick={handleClinicCardClick}
              sx={{
                borderColor: theme.palette.grey[300],
                color: theme.palette.text.secondary,
                '&:hover': {
                  borderColor: theme.palette.grey[400],
                  backgroundColor: theme.palette.grey[100],
                },
              }}
            >
              See More
            </Button>
          </Box>
        </CardContent>
      </Box>
    </>
  );
};

export default ResultCard;
