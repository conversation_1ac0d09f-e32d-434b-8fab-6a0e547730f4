{"name": "selfpay-health-client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "vitest", "test:watch": "vitest --watch"}, "dependencies": {"@ai-sdk/react": "1.2.12", "@emotion/react": "11.14.0", "@emotion/styled": "11.14.0", "@mui/icons-material": "7.1.0", "@mui/material": "7.1.0", "@next/third-parties": "15.3.1", "@portabletext/react": "3.2.1", "@sanity/image-url": "1.0.2", "ai": "4.3.15", "axios": "1.9.0", "fast-deep-equal": "3.1.3", "just-debounce-it": "3.2.0", "marked": "15.0.11", "next": "15.1.7", "next-sanity": "9.10.2", "react": "19.0.0", "react-dom": "19.0.0", "react-markdown": "10.1.0"}, "devDependencies": {"@eslint/eslintrc": "3", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.3.0", "@testing-library/user-event": "14.6.1", "@types/node": "20", "@types/react": "19", "@types/react-dom": "19", "autoprefixer": "10.4.16", "eslint": "9.25.1", "eslint-config-next": "15.1.7", "jsdom": "26.0.0", "postcss": "8.4.32", "typescript": "5", "vitest": "3.1.1"}}